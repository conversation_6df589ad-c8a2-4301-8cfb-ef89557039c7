name: branches-bat

permissions:
  actions: write
  contents: read
  id-token: write
  packages: write
  pull-requests: write
  repository-projects: write

on:
  push:
    branches:
      - bat

jobs:
  deploy-bat:
    uses: ./.github/workflows/deploy.yml
    with:
      environment: BATCA1
      stage: batca1
      role-to-assume: ${{ vars.AWS_DBAAS_BAT_OIDC_ROLE_ARN }}
      branch: bat
    secrets: inherit