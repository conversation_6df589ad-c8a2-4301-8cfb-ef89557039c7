name: branches-dev

permissions:
  actions: write
  contents: read
  id-token: write
  packages: write
  pull-requests: write
  repository-projects: write

on:
  push:
    branches:
      - dev

jobs:
  deploy-dev:
    uses: ./.github/workflows/deploy.yml
    with:
      environment: INTCA1
      stage: intca1
      role-to-assume: ${{ vars.AWS_DBAAS_INT_OIDC_ROLE_ARN }}
      branch: dev
    secrets: inherit