name: branches-feature

permissions:
  actions: write
  contents: read
  id-token: write
  packages: write
  pull-requests: write
  repository-projects: write

on:
  push:
    branches: feature/*

jobs:
  deploy-feature:
    uses: ./.github/workflows/deploy.yml
    with:
      environment: INTCA1
      stage: intca1
      role-to-assume: ${{ vars.AWS_DBAAS_INT_OIDC_ROLE_ARN }}
      branch: feature
    secrets: inherit
