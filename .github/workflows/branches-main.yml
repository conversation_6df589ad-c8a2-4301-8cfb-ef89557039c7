name: branches-release

permissions:
  actions: write
  contents: read
  id-token: write
  packages: write
  pull-requests: write
  repository-projects: write

on:
  push:
    branches:
      - main

jobs:
  deploy-prod:
    uses: ./.github/workflows/deploy.yml
    with:
      environment: PRODCA1
      stage: prodca1
      role-to-assume: ${{ vars.AWS_DBAAS_PROD_OIDC_ROLE_ARN }}
      branch: main
    secrets: inherit