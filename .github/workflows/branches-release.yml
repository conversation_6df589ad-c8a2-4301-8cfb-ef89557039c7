name: branches-release

permissions:
  actions: write
  contents: read
  id-token: write
  packages: write
  pull-requests: write
  repository-projects: write

on:
  push:
    branches:
      - release

jobs:
  deploy-crt:
    uses: ./.github/workflows/deploy.yml
    with:
      environment: CRTCA1
      stage: crtca1
      role-to-assume: ${{ vars.AWS_DBAAS_CRT_OIDC_ROLE_ARN }}
      branch: release
    secrets: inherit

  deploy-preprod:
    uses: ./.github/workflows/deploy.yml
    with:
      environment: PREPRODCA1
      stage: preprodca1
      role-to-assume: ${{ vars.AWS_DBAAS_PREPROD_OIDC_ROLE_ARN }}
      branch: release
    secrets: inherit