name: Deploy Stacks

on:
  workflow_call:
    inputs:
      environment:
        required: true
        type: string
      stage:
        required: true
        type: string
      role-to-assume:
        required: true
        type: string
      branch:
        required: true
        type: string

jobs:
  deploy:
    runs-on: ubuntu-latest
    container:
      image: atlassian/default-image:4
    environment:
      name: ${{ inputs.environment }}
    strategy:
      matrix:
        stack:
          - resources
          - fsum
          - aflvfl
          - fdm
          - pnr
          - ssm
          - compensation
          - meal-voucher
          - cm-feed
          - cp-cl1a
          - certificate-status
          - cobrand-card
          - account-merge
          - account-status
          - cp-cl1b
          - frozen-stage
          - ifly-tierstatus
          - ifly-total-bal
          - membership-status
          - pool
          - pool-balance
          - pool-permission
          - pool-redemption
          - profile
          - retail-partnership
          - retroclaim
          - smartload
          - ccm-aes
          - ccm-ctr
          - hotel
          - transportation-voucher
          - adm
          - alg
          - csg
          - flight-tracking
          - fraud-screening
          - jct-roster-output
          - tkt
    steps:
      - uses: actions/checkout@v4.1.0

      - name: Set up SSH
        run: |
          home_dir=$(eval echo ~$(whoami))
          mkdir -p $home_dir/.ssh
          echo "$SSH_PRIVATE_KEY" > $home_dir/.ssh/${{ vars.SA_GH_AUTH_SSH_PRIVATE_KEY_FILE_NAME  }}
          chmod 400 $home_dir/.ssh/${{ vars.SA_GH_AUTH_SSH_PRIVATE_KEY_FILE_NAME  }}
          echo ${{ vars.GH_HOST_FINGERPRINT }} >> $home_dir/.ssh/known_hosts
        env:
          SSH_PRIVATE_KEY: ${{ secrets.SERVICE_AC_PVT_SSH_KEY }}

      - name: Install dependencies
        run: |
          apt-get update
          apt-get install -y python3 python3-pip bash jq git openssh-client
          npm install -g serverless@2.72.3

      - name: Checkout Pre-Requisite
        uses: actions/checkout@v4
        with:
          repository: AC-IT-Development/m3-pipeline-library.git
          ref: develop
          token: ${{ secrets.SERVICEACCOUNT_PAT_TOKEN }}
          path: m3-pipeline-library

      - name: Set AWS Session Name
        id: repo_name
        run: echo "::set-output name=repo_name::$(echo ${{ github.repository }} | cut -d '/' -f 2)"

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-region: ${{ vars.AWS_DEFAULT_REGION }}
          role-to-assume: ${{ inputs.role-to-assume }}
          role-session-name: "${{ steps.repo_name.outputs.REPO_NAME }}-SLS-Deployment"

      - name: Deploy stack
        run: |
          originalPath=$PWD
          cd stacks/${{ matrix.stack }}/
          sls deploy --stage ${{ inputs.stage }} --region ca-central-1 --verbose || echo "Stack ${{ matrix.stack }} failed to deploy"