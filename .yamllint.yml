---
# YAM<PERSON>t configuration for MSK Connect infrastructure
extends: default

rules:
  # Allow longer lines for CloudFormation templates
  line-length:
    max: 120
    level: warning
  
  # Allow empty values for optional CloudFormation properties
  empty-values:
    forbid-in-block-mappings: false
    forbid-in-flow-mappings: false
  
  # Allow truthy values for CloudFormation boolean properties
  truthy:
    allowed-values: ['true', 'false', 'on', 'off', 'yes', 'no']
    check-keys: false
  
  # Allow comments for documentation
  comments:
    min-spaces-from-content: 1
  
  # Allow indentation for CloudFormation nested structures
  indentation:
    spaces: 2
    indent-sequences: true
    check-multi-line-strings: false
  
  # Allow brackets for CloudFormation functions
  brackets:
    min-spaces-inside: 0
    max-spaces-inside: 1
  
  # Allow braces for CloudFormation intrinsic functions
  braces:
    min-spaces-inside: 0
    max-spaces-inside: 1
  
  # Allow document start for CloudFormation templates
  document-start: disable
  
  # Allow key duplicates (CloudFormation may have logical duplicates)
  key-duplicates: enable
  
  # Allow octal values for file permissions
  octal-values: disable
