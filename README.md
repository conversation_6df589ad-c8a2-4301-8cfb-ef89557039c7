### How to add custom plugin on msk? ###
* step 1: Download plugin:
    - Plugin url: https://www.confluent.io/hub/confluentinc/kafka-connect-s3
    - Click on button download
* step 2: Upload plugin to s3
    - After upload get URI of plugin file on s3
* step 3: Create new custom plugin for MSK service
    - Go to Aws MSK service on MSK connect tab go to custom plugins tab

    ![Msk custom plugin tab](/images/msk_custom_plugin.png)
    - Click "Create custom plugin" button
    
    ![Add new Msk custom plugin](images/msk_custom_plugin_add.png)
    
    ```
    - Fill in "S3 URI - Custom plugin object" input is plugin uri on step 1
    - Fill all requirement input
    - Click "Create custom plugin" button
    ```
* step 4: Get `ARN` of msk custom service on step 3 and define on `CUSTOM_PLUGIN_ARN` variable on `defaults.yml`


### GitHub Actions ###

Using GitHub Matrix to run multiple jobs in parallel. Each branch will run the same job with different environment variables. `deploy.yml` is the shared workflow file for all branches. Files that starts with `branches-*` are the environment specific files.

- `deploy.yml` - Shared workflow file
- `branches-dev.yml` - Environment specific file for `dev` branch, deploys to the `INT` environment.
- `branches-bat.yml` - Environment specific file for `bat` branch, deploys to the `BAT` environment.
- `branches-feature.yml` - Environment specific file for every branch that starts with `feature/*`. It deploys to the INT environment. Use this for the development and testing. 
- `branches-release.yml` - Environment specific file for `release` branch. It deploys to the `CRT` and `PREPROD` environments. Use this for the final testing before the release.
- `branches-main.yml` - Environment specific file for `main` branch. It deploys to the `PROD` environment. Use this for the production deployment.
- `branches-hotfix.yml` - Environment specific file for every branch that starts with `hotfix/*`.  It deploys to the `CRT` environment. 