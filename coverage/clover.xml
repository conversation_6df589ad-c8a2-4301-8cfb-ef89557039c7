<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1752230001538" clover="3.2.0">
  <project timestamp="1752230001538" name="All files">
    <metrics statements="29" coveredstatements="26" conditionals="6" coveredconditionals="4" methods="10" coveredmethods="10" elements="45" coveredelements="40" complexity="0" loc="29" ncloc="29" packages="1" files="1" classes="1"/>
    <file name="yamlLoader.js" path="/Users/<USER>/Documents/Aircanada/ac-odh-event-streaming-services-msk-connect/tests/utils/yamlLoader.js">
      <metrics statements="29" coveredstatements="26" conditionals="6" coveredconditionals="4" methods="10" coveredmethods="10"/>
      <line num="5" count="2" type="stmt"/>
      <line num="6" count="2" type="stmt"/>
      <line num="7" count="2" type="stmt"/>
      <line num="8" count="2" type="stmt"/>
      <line num="12" count="2" type="stmt"/>
      <line num="16" count="2" type="cond" truecount="1" falsecount="1"/>
      <line num="17" count="0" type="stmt"/>
      <line num="20" count="2" type="stmt"/>
      <line num="21" count="2" type="stmt"/>
      <line num="22" count="2" type="stmt"/>
      <line num="23" count="2" type="stmt"/>
      <line num="24" count="2" type="stmt"/>
      <line num="26" count="0" type="stmt"/>
      <line num="32" count="1" type="stmt"/>
      <line num="33" count="1" type="stmt"/>
      <line num="37" count="1" type="stmt"/>
      <line num="38" count="1" type="stmt"/>
      <line num="42" count="1" type="stmt"/>
      <line num="43" count="1" type="stmt"/>
      <line num="45" count="38" type="stmt"/>
      <line num="46" count="38" type="cond" truecount="2" falsecount="0"/>
      <line num="52" count="1" type="stmt"/>
      <line num="53" count="1" type="cond" truecount="1" falsecount="1"/>
      <line num="54" count="0" type="stmt"/>
      <line num="57" count="1" type="stmt"/>
      <line num="58" count="3" type="stmt"/>
      <line num="59" count="3" type="stmt"/>
      <line num="63" count="2" type="stmt"/>
      <line num="67" count="2" type="stmt"/>
    </file>
  </project>
</coverage>
