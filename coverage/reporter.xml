<?xml version="1.0" encoding="UTF-8"?>
<testExecutions version="1">
    <file path="/Users/<USER>/Documents/Aircanada/ac-odh-event-streaming-services-msk-connect/tests/infrastructure/test_glue_resources.test.js">
        <testCase name="AWS Glue Resources Infrastructure Glue Classifier Resources adm stack should have valid Grok classifier" duration="6"/>
    </file>
    <file path="/Users/<USER>/Documents/Aircanada/ac-odh-event-streaming-services-msk-connect/tests/config/test_defaults_validation.test.js">
        <testCase name="Defaults Configuration Validation Basic Structure should have required top-level properties" duration="4"/>
        <testCase name="Defaults Configuration Validation Basic Structure should have valid service name" duration="0"/>
        <testCase name="Defaults Configuration Validation Basic Structure should have all required tags" duration="1"/>
        <testCase name="Defaults Configuration Validation Environment Configuration should have all expected environments" duration="1"/>
        <testCase name="Defaults Configuration Validation Environment Configuration each environment should have required common parameters" duration="10"/>
        <testCase name="Defaults Configuration Validation Environment Configuration AWS account IDs should be valid" duration="1"/>
        <testCase name="Defaults Configuration Validation Environment Configuration S3 bucket names should follow naming convention" duration="0"/>
        <testCase name="Defaults Configuration Validation Environment Configuration VPC security groups should be valid format" duration="0"/>
        <testCase name="Defaults Configuration Validation Environment Configuration VPC subnets should be valid format" duration="1"/>
    </file>
</testExecutions>