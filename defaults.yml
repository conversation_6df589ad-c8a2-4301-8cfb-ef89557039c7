############################################################
# How to add a new MSK event stream
# Add stack
# Fix variable with default.yml
# Add lifecycle rule to stacks/resources/serverless.yml
############################################################

service: ac-odh-event-streaming-services-msk-connect

custom:
  tags:
    project: Air Canada Argo ODH
    application: Kafka to S3 Transfer MSK Connector
    repository: https://github.com/AC-IT-Development/ac-odh-event-streaming-services-msk-connect
    environment: ${self:provider.stage}
    tower: operations
    department-id: 1904
    department-name: ops-digital
    service: ac-odh-event-streaming-services-msk-connect
    CostCode: 1904
    ProjectName: ODH
    SharedResource: No
    TechOwner: Pradeep Nishantha
    BusinessOwner: Prade<PERSON>tha
    Environment: ${self:provider.stage}
    Criticality: Major
    Sensitivity: High
    RecoveryTimeObjective: 60 mins
    RecoveryPointObjective: 15 mins
    Type: Operations
    BusinessImpact: High
    ComplianceRequirement: NA
    Observability: No

  params:
    intca1:
      DEBUG_MODE: true
      # AWS Account Id of EC2 Instance (PowerBI Windows Gateway)
      POWER_BI_AWS_ACCOUNT: ************

      # Kafka connector autoscaling capacity configs (we are not using this due to limitations)
      CAPACITY_AUTO_SCALING_MAX_WORKER_COUNT: 2
      CAPACITY_AUTO_SCALING_MCU_COUNT: 1
      CAPACITY_AUTO_SCALING_MIN_WORDER_COUNT: 1
      CAPACITY_AUTO_SCALING_SCALE_IN_POLICY_CPU_UTIL_PERCENTAGE: 20
      CAPACITY_AUTO_SCALING_SCALE_OUT_POLICY_CPU_UTIL_PERCENTAGE: 80

      # Kafka connector provisioned capacity configs
      CAPACITY_PROVISIONED_CAPACITY_WORKER_COUNT: 1
      CAPACITY_PROVISIONED_CAPACITY_MCU_COUNT: 1

      # common configs
      CONNECTOR_CONFIGURATION_FLUSH_SIZE: 1
      CONNECTOR_CONFIGURATION_BUCKET: ac-odh-stream-event-store-intca1
      SERVICE_EXECUTION_ROLE_ARN: arn:aws:kafka:ca-central-1:************:cluster/digital-msk-intca1/efd13acf-2227-45ec-a32d-43c225aadd51-3

      # Origin MKS Cluster
      ORIGIN_BOOTSTRAP_SERVER: b-2.digital-msk-intca1.l0hmxz.c3.kafka.ca-central-1.amazonaws.com:9094,b-3.digital-msk-intca1.l0hmxz.c3.kafka.ca-central-1.amazonaws.com:9094,b-1.digital-msk-intca1.l0hmxz.c3.kafka.ca-central-1.amazonaws.com:9094
      ORIGIN_VPC_SG: sg-0f60f565c8da7ab17
      ORIGIN_VPC_SUBNET_A: subnet-0656557afe0f2bc5e
      ORIGIN_VPC_SUBNET_B: subnet-07612cb342a5be91d
      ORIGIN_VPC_SUBNET_C: subnet-01644fc5d5a72b235
      CUSTOM_PLUGIN_ARN: arn:aws:kafkaconnect:ca-central-1:************:custom-plugin/odh-raw-event-persister-plugin-central-1/b601afe2-b2e7-4eae-815a-a42d2554190f-3
      CUSTOM_PLUGIN_REVISION: 1

      # fsum
      CONNECTOR_CONFIGURATION_TOPIC_FSUM_DIR: fsum
      CONNECTOR_CONFIGURATION_TOPIC_FSUM: RAW-FSUM-LIDO-INTCA1
      CONNECTOR_CONFIGURATION_FORMAT_FSUM_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_FSUM: 30

      # alg
      CONNECTOR_CONFIGURATION_TOPIC_ALG_DIR: alg
      CONNECTOR_CONFIGURATION_TOPIC_ALG: RAW-ALG-SMARTSUITE-INTCA1
      CONNECTOR_CONFIGURATION_FORMAT_ALG_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_ALG: 30

      # aflvfl
      CONNECTOR_CONFIGURATION_TOPIC_AFLVFL_DIR: aflvfl
      CONNECTOR_CONFIGURATION_TOPIC_AFLVFL: RAW-AFLVFL-AIRCOM-INTCA1
      CONNECTOR_CONFIGURATION_FORMAT_AFLVFL_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_AFLVFL: 30

      # pnr
      CONNECTOR_CONFIGURATION_TOPIC_PNR_DIR: pnr
      CONNECTOR_CONFIGURATION_TOPIC_PNR: RAW-PNR-ADH-INTCA1
      CONNECTOR_CONFIGURATION_FORMAT_PNR_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_PNR: 30

      # csg
      CONNECTOR_CONFIGURATION_TOPIC_CSG_DIR: csg
      CONNECTOR_CONFIGURATION_TOPIC_CSG: RAW-CSG-SMARTSUITE-INTCA1
      CONNECTOR_CONFIGURATION_FORMAT_CSG_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_CSG: 30

      # fdm
      CONNECTOR_CONFIGURATION_TOPIC_FDM_DIR: fdm
      CONNECTOR_CONFIGURATION_TOPIC_FDM: RAW-FDM-NLOPS-INTCA1
      CONNECTOR_CONFIGURATION_FORMAT_FDM_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_FDM: 30

      # adm
      CONNECTOR_CONFIGURATION_TOPIC_ADM_DIR: adm
      CONNECTOR_CONFIGURATION_TOPIC_ADM: msk-dev.EAI-ADM-INT
      CONNECTOR_CONFIGURATION_FORMAT_ADM_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_ADM: 30

      # ssm
      CONNECTOR_CONFIGURATION_TOPIC_SSM_DIR: ssm
      CONNECTOR_CONFIGURATION_TOPIC_SSM: RAW-SSM-SM-INTCA1
      CONNECTOR_CONFIGURATION_FORMAT_SSM_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_SSM: 45

      # hotel accepted/declined
      CONNECTOR_CONFIGURATION_TOPIC_HA_DIR: hotel-accepted
      CONNECTOR_CONFIGURATION_TOPIC_HA: RAW-COMPENSATION-STORMX-INTCA1
      CONNECTOR_CONFIGURATION_FORMAT_HA_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_HA: 30
      CRAWLER_TARGET_PATH: ac-odh-event-store-intca1/hotel-accepted/RAW-COMPENSATION-STORMX-INTCA1

      # meal voucher
      CONNECTOR_CONFIGURATION_TOPIC_MV_DIR: meal-voucher
      CONNECTOR_CONFIGURATION_TOPIC_MV: RAW-COMPENSATION-ICOUPONMEAL-INTCA1
      CONNECTOR_CONFIGURATION_FORMAT_MV_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_MV: 30

      # account merge
      CONNECTOR_CONFIGURATION_TOPIC_AM_DIR: account-merge
      CONNECTOR_CONFIGURATION_TOPIC_AM: RAW-ACCOUNTMERGE-IFLY-INTCA1
      CONNECTOR_CONFIGURATION_FORMAT_AM_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_AM: 30

      # account status
      CONNECTOR_CONFIGURATION_TOPIC_AS_DIR: account-status
      CONNECTOR_CONFIGURATION_TOPIC_AS: RAW-ACCOUNTSTATUS-IFLY-INTCA1
      CONNECTOR_CONFIGURATION_FORMAT_AS_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_AS: 30

      # certificate status
      CONNECTOR_CONFIGURATION_TOPIC_CF_STATUS_DIR: certificate-status
      CONNECTOR_CONFIGURATION_TOPIC_CF_STATUS: RAW-CERTIFICATESTATUS-IFLY-INTCA1
      CONNECTOR_CONFIGURATION_FORMAT_CF_STATUS_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_CF_STATUS: 30

      # cm feed
      CONNECTOR_CONFIGURATION_TOPIC_CM_FEED_DIR: cm-feed
      CONNECTOR_CONFIGURATION_TOPIC_CM_FEED: RAW-DCSPAX-ADH-INTCA1
      CONNECTOR_CONFIGURATION_FORMAT_CM_FEED_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_CM_FEED: 30

      # cobrand card
      CONNECTOR_CONFIGURATION_TOPIC_CC_DIR: cobrand-card
      CONNECTOR_CONFIGURATION_TOPIC_CC: RAW-COBRANDCARD-IFLY-INTCA1
      CONNECTOR_CONFIGURATION_FORMAT_CC_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_CC: 30

      # cp cl1a
      CONNECTOR_CONFIGURATION_TOPIC_CP_CL1A_DIR: cp-cl1a
      CONNECTOR_CONFIGURATION_TOPIC_CP_CL1A: RAW-CL1A-GIGYA-INTCA1
      CONNECTOR_CONFIGURATION_FORMAT_CP_CL1A_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_CP_CL1A: 30

      # cp cl1b
      CONNECTOR_CONFIGURATION_TOPIC_CP_CL1B_DIR: cp-cl1b
      CONNECTOR_CONFIGURATION_TOPIC_CP_CL1B: RAW-CL1B-GIGYA-INTCA1
      CONNECTOR_CONFIGURATION_FORMAT_CP_CL1B_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_CP_CL1B: 30

      # frozen stage
      CONNECTOR_CONFIGURATION_TOPIC_FROZEN_STAGE_DIR: frozen-stage
      CONNECTOR_CONFIGURATION_TOPIC_FROZEN_STAGE: RAW-FROZENSTAGE-IFLY-INTCA1
      CONNECTOR_CONFIGURATION_FORMAT_FROZEN_STAGE_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_FROZEN_STAGE: 30

      # ifly tierstatus
      CONNECTOR_CONFIGURATION_TOPIC_TIERSTATUS_IFLY_DIR: ifly-tierstatus
      CONNECTOR_CONFIGURATION_TOPIC_TIERSTATUS_IFLY: RAW-TIERSTATUS-IFLY-INTCA1
      CONNECTOR_CONFIGURATION_FORMAT_TIERSTATUS_IFLY_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_TIERSTATUS_IFLY: 30

      # ifly total bal
      CONNECTOR_CONFIGURATION_TOPIC_TOTAL_BAL_DIR: ifly-total-bal
      CONNECTOR_CONFIGURATION_TOPIC_TOTAL_BAL: RAW-TOTALBALANCE-IFLY-INTCA1
      CONNECTOR_CONFIGURATION_FORMAT_TOTAL_BAL_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_TOTAL_BAL: 30

      # hotels catalog
      CONNECTOR_CONFIGURATION_TOPIC_HOTEL_CATALOG_DATA_DIR: hotel-catalog-data
      S3_EVENTS_EXPIRATION_DAYS_HOTEL_CATALOG_DATA: 30

      # jct roster output
      CONNECTOR_CONFIGURATION_TOPIC_JCT_ROSTER_OUTPUT_DIR: jct-roster-output
      CONNECTOR_CONFIGURATION_TOPIC_JCT_ROSTER_OUTPUT: RAW-ROSTER-JCT-INTCA1
      CONNECTOR_CONFIGURATION_FORMAT_JCT_ROSTER_OUTPUT_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_JCT_ROSTER_OUTPUT: 30

      #monthly block limit
      CONNECTOR_CONFIGURATION_TOPIC_MONTHLY_BLOCK_LIMIT_DIR: monthly-block-limit
      S3_EVENTS_EXPIRATION_DAYS_MONTHLY_BLOCK_LIMIT: 30

      # membership status
      CONNECTOR_CONFIGURATION_TOPIC_MEMBERSHIP_STATUS_DIR: membership-status
      CONNECTOR_CONFIGURATION_TOPIC_MEMBERSHIP_STATUS: RAW-MEMBERSHIPSTATUS-IFLY-INTCA1
      CONNECTOR_CONFIGURATION_FORMAT_MEMBERSHIP_STATUS_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_MEMBERSHIP_STATUS: 30

      # pool
      CONNECTOR_CONFIGURATION_TOPIC_POOL_DIR: pool
      CONNECTOR_CONFIGURATION_TOPIC_POOL: RAW-POOL-IFLY-INTCA1
      CONNECTOR_CONFIGURATION_FORMAT_POOL_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_POOL: 30

      # pool-balance
      CONNECTOR_CONFIGURATION_TOPIC_POOL_BALANCE_DIR: pool-balance
      CONNECTOR_CONFIGURATION_TOPIC_POOL_BALANCE: RAW-POOLBALANCE-IFLY-INTCA1
      CONNECTOR_CONFIGURATION_FORMAT_POOL_BALANCE_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_POOL_BALANCE: 30

      # pool-permission
      CONNECTOR_CONFIGURATION_TOPIC_POOL_PERMISSION_DIR: pool-permission
      CONNECTOR_CONFIGURATION_TOPIC_POOL_PERMISSION: RAW-POOLPERMISSION-IFLY-INTCA1
      CONNECTOR_CONFIGURATION_FORMAT_POOL_PERMISSION_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_POOL_PERMISSION: 30

      # pool-redemption
      CONNECTOR_CONFIGURATION_TOPIC_POOL_REDEMPTION_DIR: pool-redemption
      CONNECTOR_CONFIGURATION_TOPIC_POOL_REDEMPTION: RAW-POOLREDEMPTION-IFLY-INTCA1
      CONNECTOR_CONFIGURATION_FORMAT_POOL_REDEMPTION_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_POOL_REDEMPTION: 30

      # profile
      CONNECTOR_CONFIGURATION_TOPIC_PROFILE_DIR: profile
      CONNECTOR_CONFIGURATION_TOPIC_PROFILE: RAW-PROFILE-IFLY-INTCA1
      CONNECTOR_CONFIGURATION_FORMAT_PROFILE_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_PROFILE: 30

      # retail-partnership
      CONNECTOR_CONFIGURATION_TOPIC_RETAIL_PARTNERSHIP_DIR: retail-partnership
      CONNECTOR_CONFIGURATION_TOPIC_RETAIL_PARTNERSHIP: RAW-RETAILPARTNERSHIP-IFLY-INTCA1
      CONNECTOR_CONFIGURATION_FORMAT_RETAIL_PARTNERSHIP_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_RETAIL_PARTNERSHIP: 30

      # retroclaim
      CONNECTOR_CONFIGURATION_TOPIC_RETROCLAIM_DIR: retroclaim
      CONNECTOR_CONFIGURATION_TOPIC_RETROCLAIM: RAW-RETROCLAIM-IFLY-INTCA1
      CONNECTOR_CONFIGURATION_FORMAT_RETROCLAIM_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_RETROCLAIM: 30

      # smartload
      CONNECTOR_CONFIGURATION_TOPIC_SMARTLOAD_DIR: smartload
      CONNECTOR_CONFIGURATION_TOPIC_SMARTLOAD: RAW-WAREHOUSEDATA-S4A-SMTLD-INTCA1
      CONNECTOR_CONFIGURATION_FORMAT_SMARTLOAD_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_SMARTLOAD: 30

      # ccm-aes
      CONNECTOR_CONFIGURATION_TOPIC_CCM_AES_DIR: ccm-aes
      CONNECTOR_CONFIGURATION_TOPIC_CCM_AES: RAW-AES-CC-INTCA1
      CONNECTOR_CONFIGURATION_FORMAT_CCM_AES_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_CCM_AES: 30

      # ccm-ctr
      CONNECTOR_CONFIGURATION_TOPIC_CCM_CTR_DIR: ccm-ctr
      CONNECTOR_CONFIGURATION_TOPIC_CCM_CTR: RAW-CTR-CC-INTCA1
      CONNECTOR_CONFIGURATION_FORMAT_CCM_CTR_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_CCM_CTR: 30

      # hotel cancellation
      CONNECTOR_CONFIGURATION_TOPIC_HC_DIR: hotel-cancellation
      CONNECTOR_CONFIGURATION_TOPIC_HC: RAW-COMPENSATION-DBAASHOTEL-INTCA1
      CONNECTOR_CONFIGURATION_FORMAT_HC_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_HC: 30

      # transportation voucher
      CONNECTOR_CONFIGURATION_TOPIC_TV_DIR: transportation-voucher
      CONNECTOR_CONFIGURATION_TOPIC_TV: RAW-COMPENSATION-ICOUPONTRANSPORTATION-INTCA1
      CONNECTOR_CONFIGURATION_FORMAT_TV_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_TV: 30

      # Flight Tracking
      CONNECTOR_CONFIGURATION_TOPIC_FLIGHT_EVENT_FLIGHTAWARE_DIR: flight-event-flightaware
      CONNECTOR_CONFIGURATION_TOPIC_FLIGHT_EVENT_FLIGHTAWARE: emh-dev.MULE-FLIGHTAWARE-FLIGHTEVENTS-INT
      CONNECTOR_CONFIGURATION_FORMAT_FLIGHT_EVENT_FLIGHTAWARE_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_FLIGHT_EVENT_FLIGHTAWARE: 30

      CONNECTOR_CONFIGURATION_TOPIC_WEATHER_EVENT_FLIGHTAWARE_DIR: weather-event-flightaware
      CONNECTOR_CONFIGURATION_TOPIC_WEATHER_EVENT_FLIGHTAWARE: emh-dev.MULE-FLIGHTAWARE-WEATHER-INT
      CONNECTOR_CONFIGURATION_FORMAT_WEATHER_EVENT_FLIGHTAWARE_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_WEATHER_EVENT_FLIGHTAWARE: 30

      CONNECTOR_CONFIGURATION_TOPIC_FLIGHT_EVENT_AR24_DIR: flight-event-ar24
      CONNECTOR_CONFIGURATION_TOPIC_FLIGHT_EVENT_AR24: emh-dev.MULE-FLIGHTAWARE-FR24-INT
      CONNECTOR_CONFIGURATION_FORMAT_FLIGHT_EVENT_AR24_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_FLIGHT_EVENT_AR24: 30

      # fraud screening
      CONNECTOR_CONFIGURATION_TOPIC_FRAUD_SCREENING_DIR: fraud-screening
      CONNECTOR_CONFIGURATION_TOPIC_FRAUD_SCREENING: emh-dev.MULE-XPAY-PAYMENTSTATUS-INT
      CONNECTOR_CONFIGURATION_FORMAT_FRAUD_SCREENING_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_FRAUD_SCREENING: 30

      # tkt
      CONNECTOR_CONFIGURATION_TOPIC_TKT_DIR: tkt
      CONNECTOR_CONFIGURATION_TOPIC_TKT: RAW-TKT-INTCA1
      CONNECTOR_CONFIGURATION_FORMAT_TKT_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_TKT: 30

      # XFSU
      CONNECTOR_CONFIGURATION_TOPIC_XFSU_DIR: xfsu
      CONNECTOR_CONFIGURATION_TOPIC_XFSU: emh-dev.ECARGO-FSU-JSON-INT
      CONNECTOR_CONFIGURATION_FORMAT_XFSU_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      
      CRAWLER_ROLE_NAME: ac-odh-event-streaming-services-msk-connect-res-intca1-catalog
    
    batca1:
      DEBUG_MODE: true
      # AWS Account Id of EC2 Instance (PowerBI Windows Gateway)
      POWER_BI_AWS_ACCOUNT: ************

      # Kafka connector autoscaling capacity configs (we are not using this due to limitations)
      CAPACITY_AUTO_SCALING_MAX_WORKER_COUNT: 2
      CAPACITY_AUTO_SCALING_MCU_COUNT: 1
      CAPACITY_AUTO_SCALING_MIN_WORDER_COUNT: 1
      CAPACITY_AUTO_SCALING_SCALE_IN_POLICY_CPU_UTIL_PERCENTAGE: 20
      CAPACITY_AUTO_SCALING_SCALE_OUT_POLICY_CPU_UTIL_PERCENTAGE: 80

      # Kafka connector provisioned capacity configs
      CAPACITY_PROVISIONED_CAPACITY_WORKER_COUNT: 1
      CAPACITY_PROVISIONED_CAPACITY_MCU_COUNT: 1

      # common configs
      CONNECTOR_CONFIGURATION_FLUSH_SIZE: 1
      CONNECTOR_CONFIGURATION_BUCKET: ac-odh-stream-event-store-batca1
      SERVICE_EXECUTION_ROLE_ARN: arn:aws:kafka:ca-central-1:************:cluster/digital-msk-batca1/6415732f-1c7d-4e62-aacf-95c089a0ef75-2

      # Origin MKS Cluster
      ORIGIN_BOOTSTRAP_SERVER: b-1.digitalmskbatca1.arylnm.c2.kafka.ca-central-1.amazonaws.com:9094,b-2.digitalmskbatca1.arylnm.c2.kafka.ca-central-1.amazonaws.com:9094,b-3.digitalmskbatca1.arylnm.c2.kafka.ca-central-1.amazonaws.com:9094
      ORIGIN_VPC_SG: sg-0c6a2bb9f40fe2d66
      ORIGIN_VPC_SUBNET_A: subnet-093ec232ccaaf9236
      ORIGIN_VPC_SUBNET_B: subnet-0e040f56cf4be65bc
      ORIGIN_VPC_SUBNET_C: subnet-0fd7784cf1d74d946
      CUSTOM_PLUGIN_ARN: arn:aws:kafkaconnect:ca-central-1:************:custom-plugin/odh-raw-event-persister-plugin-central-1/21d8ac75-b017-4ba8-bfdb-d35f6adaff06-2
      CUSTOM_PLUGIN_REVISION: 1

      # fsum
      CONNECTOR_CONFIGURATION_TOPIC_FSUM_DIR: fsum
      CONNECTOR_CONFIGURATION_TOPIC_FSUM: RAW-FSUM-LIDO-BATCA1
      CONNECTOR_CONFIGURATION_FORMAT_FSUM_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_FSUM: 30

      # alg
      CONNECTOR_CONFIGURATION_TOPIC_ALG_DIR: alg
      CONNECTOR_CONFIGURATION_TOPIC_ALG: RAW-ALG-SMARTSUITE-BATCA1
      CONNECTOR_CONFIGURATION_FORMAT_ALG_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_ALG: 30

      # aflvfl
      CONNECTOR_CONFIGURATION_TOPIC_AFLVFL_DIR: aflvfl
      CONNECTOR_CONFIGURATION_TOPIC_AFLVFL: RAW-AFLVFL-AIRCOM-BATCA1
      CONNECTOR_CONFIGURATION_FORMAT_AFLVFL_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_AFLVFL: 30

      # pnr
      CONNECTOR_CONFIGURATION_TOPIC_PNR_DIR: pnr
      CONNECTOR_CONFIGURATION_TOPIC_PNR: RAW-PNR-ADH-BATCA1
      CONNECTOR_CONFIGURATION_FORMAT_PNR_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_PNR: 30

      # csg
      CONNECTOR_CONFIGURATION_TOPIC_CSG_DIR: csg
      CONNECTOR_CONFIGURATION_TOPIC_CSG: RAW-CSG-SMARTSUITE-BATCA1
      CONNECTOR_CONFIGURATION_FORMAT_CSG_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_CSG: 30

      # fdm
      CONNECTOR_CONFIGURATION_TOPIC_FDM_DIR: fdm
      CONNECTOR_CONFIGURATION_TOPIC_FDM: RAW-FDM-NLOPS-BATCA1
      CONNECTOR_CONFIGURATION_FORMAT_FDM_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_FDM: 30

      # adm
      CONNECTOR_CONFIGURATION_TOPIC_ADM_DIR: adm
      CONNECTOR_CONFIGURATION_TOPIC_ADM: emh-dev.EAI-ADM-UAT
      CONNECTOR_CONFIGURATION_FORMAT_ADM_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_ADM: 30

      # ssm
      CONNECTOR_CONFIGURATION_TOPIC_SSM_DIR: ssm
      CONNECTOR_CONFIGURATION_TOPIC_SSM: RAW-SSM-SM-BATCA1
      CONNECTOR_CONFIGURATION_FORMAT_SSM_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_SSM: 45

      # hotel accepted/declined
      CONNECTOR_CONFIGURATION_TOPIC_HA_DIR: hotel-accepted
      CONNECTOR_CONFIGURATION_TOPIC_HA: RAW-COMPENSATION-STORMX-BATCA1
      CONNECTOR_CONFIGURATION_FORMAT_HA_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_HA: 30
      CRAWLER_TARGET_PATH: ac-odh-event-store-batca1/hotel-accepted/RAW-COMPENSATION-STORMX-BATCA1

      # meal voucher
      CONNECTOR_CONFIGURATION_TOPIC_MV_DIR: meal-voucher
      CONNECTOR_CONFIGURATION_TOPIC_MV: RAW-COMPENSATION-ICOUPONMEAL-BATCA1
      CONNECTOR_CONFIGURATION_FORMAT_MV_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_MV: 30

      # account merge
      CONNECTOR_CONFIGURATION_TOPIC_AM_DIR: account-merge
      CONNECTOR_CONFIGURATION_TOPIC_AM: RAW-ACCOUNTMERGE-IFLY-BATCA1
      CONNECTOR_CONFIGURATION_FORMAT_AM_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_AM: 30

      # account status
      CONNECTOR_CONFIGURATION_TOPIC_AS_DIR: account-status
      CONNECTOR_CONFIGURATION_TOPIC_AS: RAW-ACCOUNTSTATUS-IFLY-BATCA1
      CONNECTOR_CONFIGURATION_FORMAT_AS_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_AS: 30

      # certificate status
      CONNECTOR_CONFIGURATION_TOPIC_CF_STATUS_DIR: certificate-status
      CONNECTOR_CONFIGURATION_TOPIC_CF_STATUS: RAW-CERTIFICATESTATUS-IFLY-BATCA1
      CONNECTOR_CONFIGURATION_FORMAT_CF_STATUS_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_CF_STATUS: 30

      # cm feed
      CONNECTOR_CONFIGURATION_TOPIC_CM_FEED_DIR: cm-feed
      CONNECTOR_CONFIGURATION_TOPIC_CM_FEED: RAW-DCSPAX-ADH-BATCA1
      CONNECTOR_CONFIGURATION_FORMAT_CM_FEED_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_CM_FEED: 30

      # cobrand card
      CONNECTOR_CONFIGURATION_TOPIC_CC_DIR: cobrand-card
      CONNECTOR_CONFIGURATION_TOPIC_CC: RAW-COBRANDCARD-IFLY-BATCA1
      CONNECTOR_CONFIGURATION_FORMAT_CC_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_CC: 30

      # cp cl1a
      CONNECTOR_CONFIGURATION_TOPIC_CP_CL1A_DIR: cp-cl1a
      CONNECTOR_CONFIGURATION_TOPIC_CP_CL1A: RAW-CL1A-GIGYA-BATCA1
      CONNECTOR_CONFIGURATION_FORMAT_CP_CL1A_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_CP_CL1A: 30

      # cp cl1b
      CONNECTOR_CONFIGURATION_TOPIC_CP_CL1B_DIR: cp-cl1b
      CONNECTOR_CONFIGURATION_TOPIC_CP_CL1B: RAW-CL1B-2-GIGYA-BATCA1
      CONNECTOR_CONFIGURATION_FORMAT_CP_CL1B_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_CP_CL1B: 30

      # frozen stage
      CONNECTOR_CONFIGURATION_TOPIC_FROZEN_STAGE_DIR: frozen-stage
      CONNECTOR_CONFIGURATION_TOPIC_FROZEN_STAGE: RAW-FROZENSTAGE-IFLY-BATCA1
      CONNECTOR_CONFIGURATION_FORMAT_FROZEN_STAGE_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_FROZEN_STAGE: 30

      # ifly tierstatus
      CONNECTOR_CONFIGURATION_TOPIC_TIERSTATUS_IFLY_DIR: ifly-tierstatus
      CONNECTOR_CONFIGURATION_TOPIC_TIERSTATUS_IFLY: RAW-TIERSTATUS-IFLY-BATCA1
      CONNECTOR_CONFIGURATION_FORMAT_TIERSTATUS_IFLY_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_TIERSTATUS_IFLY: 30

      # ifly total bal
      CONNECTOR_CONFIGURATION_TOPIC_TOTAL_BAL_DIR: ifly-total-bal
      CONNECTOR_CONFIGURATION_TOPIC_TOTAL_BAL: RAW-TOTALBALANCE-2-IFLY-BATCA1
      CONNECTOR_CONFIGURATION_FORMAT_TOTAL_BAL_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_TOTAL_BAL: 30

      # hotels catalog
      CONNECTOR_CONFIGURATION_TOPIC_HOTEL_CATALOG_DATA_DIR: hotel-catalog-data
      S3_EVENTS_EXPIRATION_DAYS_HOTEL_CATALOG_DATA: 30

      # jct roster output
      CONNECTOR_CONFIGURATION_TOPIC_JCT_ROSTER_OUTPUT_DIR: jct-roster-output
      CONNECTOR_CONFIGURATION_TOPIC_JCT_ROSTER_OUTPUT: emh-dev.JCT-CREWROSTER-UAT
      CONNECTOR_CONFIGURATION_FORMAT_JCT_ROSTER_OUTPUT_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_JCT_ROSTER_OUTPUT: 30

      #monthly block limit
      CONNECTOR_CONFIGURATION_TOPIC_MONTHLY_BLOCK_LIMIT_DIR: monthly-block-limit
      S3_EVENTS_EXPIRATION_DAYS_MONTHLY_BLOCK_LIMIT: 30

      # membership status
      CONNECTOR_CONFIGURATION_TOPIC_MEMBERSHIP_STATUS_DIR: membership-status
      CONNECTOR_CONFIGURATION_TOPIC_MEMBERSHIP_STATUS: RAW-MEMBERSHIPSTATUS-IFLY-BATCA1
      CONNECTOR_CONFIGURATION_FORMAT_MEMBERSHIP_STATUS_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_MEMBERSHIP_STATUS: 30

      # pool
      CONNECTOR_CONFIGURATION_TOPIC_POOL_DIR: pool
      CONNECTOR_CONFIGURATION_TOPIC_POOL: RAW-POOL-IFLY-BATCA1
      CONNECTOR_CONFIGURATION_FORMAT_POOL_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_POOL: 30

      # pool-balance
      CONNECTOR_CONFIGURATION_TOPIC_POOL_BALANCE_DIR: pool-balance
      CONNECTOR_CONFIGURATION_TOPIC_POOL_BALANCE: RAW-POOLBALANCE-IFLY-BATCA1
      CONNECTOR_CONFIGURATION_FORMAT_POOL_BALANCE_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_POOL_BALANCE: 30

      # pool-permission
      CONNECTOR_CONFIGURATION_TOPIC_POOL_PERMISSION_DIR: pool-permission
      CONNECTOR_CONFIGURATION_TOPIC_POOL_PERMISSION: RAW-POOLPERMISSION-IFLY-BATCA1
      CONNECTOR_CONFIGURATION_FORMAT_POOL_PERMISSION_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_POOL_PERMISSION: 30

      # pool-redemption
      CONNECTOR_CONFIGURATION_TOPIC_POOL_REDEMPTION_DIR: pool-redemption
      CONNECTOR_CONFIGURATION_TOPIC_POOL_REDEMPTION: RAW-POOLREDEMPTION-IFLY-BATCA1
      CONNECTOR_CONFIGURATION_FORMAT_POOL_REDEMPTION_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_POOL_REDEMPTION: 30

      # profile
      CONNECTOR_CONFIGURATION_TOPIC_PROFILE_DIR: profile
      CONNECTOR_CONFIGURATION_TOPIC_PROFILE: RAW-PROFILE-IFLY-BATCA1
      CONNECTOR_CONFIGURATION_FORMAT_PROFILE_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_PROFILE: 30

      # retail-partnership
      CONNECTOR_CONFIGURATION_TOPIC_RETAIL_PARTNERSHIP_DIR: retail-partnership
      CONNECTOR_CONFIGURATION_TOPIC_RETAIL_PARTNERSHIP: RAW-RETAILPARTNERSHIP-2-IFLY-BATCA1
      CONNECTOR_CONFIGURATION_FORMAT_RETAIL_PARTNERSHIP_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_RETAIL_PARTNERSHIP: 30

      # retroclaim
      CONNECTOR_CONFIGURATION_TOPIC_RETROCLAIM_DIR: retroclaim
      CONNECTOR_CONFIGURATION_TOPIC_RETROCLAIM: RAW-RETROCLAIM-IFLY-BATCA1
      CONNECTOR_CONFIGURATION_FORMAT_RETROCLAIM_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_RETROCLAIM: 30

      # smartload
      CONNECTOR_CONFIGURATION_TOPIC_SMARTLOAD_DIR: smartload
      CONNECTOR_CONFIGURATION_TOPIC_SMARTLOAD: RAW-WAREHOUSEDATA-S4A-SMTLD-BATCA1
      CONNECTOR_CONFIGURATION_FORMAT_SMARTLOAD_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_SMARTLOAD: 30

      # ccm-aes
      CONNECTOR_CONFIGURATION_TOPIC_CCM_AES_DIR: ccm-aes
      CONNECTOR_CONFIGURATION_TOPIC_CCM_AES: RAW-AES-CC-BATCA1
      CONNECTOR_CONFIGURATION_FORMAT_CCM_AES_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_CCM_AES: 30

      # ccm-ctr
      CONNECTOR_CONFIGURATION_TOPIC_CCM_CTR_DIR: ccm-ctr
      CONNECTOR_CONFIGURATION_TOPIC_CCM_CTR: RAW-CTR-CC-BATCA1
      CONNECTOR_CONFIGURATION_FORMAT_CCM_CTR_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_CCM_CTR: 30

      # hotel cancellation
      CONNECTOR_CONFIGURATION_TOPIC_HC_DIR: hotel-cancellation
      CONNECTOR_CONFIGURATION_TOPIC_HC: RAW-COMPENSATION-DBAASHOTEL-BATCA1
      CONNECTOR_CONFIGURATION_FORMAT_HC_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_HC: 30

      # transportation voucher
      CONNECTOR_CONFIGURATION_TOPIC_TV_DIR: transportation-voucher
      CONNECTOR_CONFIGURATION_TOPIC_TV: RAW-COMPENSATION-ICOUPONTRANSPORTATION-BATCA1
      CONNECTOR_CONFIGURATION_FORMAT_TV_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_TV: 30

      # Flight Tracking
      CONNECTOR_CONFIGURATION_TOPIC_FLIGHT_EVENT_FLIGHTAWARE_DIR: flight-event-flightaware
      CONNECTOR_CONFIGURATION_TOPIC_FLIGHT_EVENT_FLIGHTAWARE: emh-dev.MULE-FLIGHTAWARE-FLIGHTEVENTS-UAT
      CONNECTOR_CONFIGURATION_FORMAT_FLIGHT_EVENT_FLIGHTAWARE_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_FLIGHT_EVENT_FLIGHTAWARE: 30

      CONNECTOR_CONFIGURATION_TOPIC_WEATHER_EVENT_FLIGHTAWARE_DIR: weather-event-flightaware
      CONNECTOR_CONFIGURATION_TOPIC_WEATHER_EVENT_FLIGHTAWARE: emh-dev.MULE-FLIGHTAWARE-WEATHER-UAT
      CONNECTOR_CONFIGURATION_FORMAT_WEATHER_EVENT_FLIGHTAWARE_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_WEATHER_EVENT_FLIGHTAWARE: 30

      CONNECTOR_CONFIGURATION_TOPIC_FLIGHT_EVENT_AR24_DIR: flight-event-ar24
      CONNECTOR_CONFIGURATION_TOPIC_FLIGHT_EVENT_AR24: emh-dev.MULE-FLIGHTAWARE-FR24-UAT
      CONNECTOR_CONFIGURATION_FORMAT_FLIGHT_EVENT_AR24_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_FLIGHT_EVENT_AR24: 30

      # fraud screening
      CONNECTOR_CONFIGURATION_TOPIC_FRAUD_SCREENING_DIR: fraud-screening
      CONNECTOR_CONFIGURATION_TOPIC_FRAUD_SCREENING: emh-dev.MULE-XPAY-PAYMENTSTATUS-UAT
      CONNECTOR_CONFIGURATION_FORMAT_FRAUD_SCREENING_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_FRAUD_SCREENING: 30

      # tkt
      CONNECTOR_CONFIGURATION_TOPIC_TKT_DIR: tkt
      CONNECTOR_CONFIGURATION_TOPIC_TKT: RAW-TKT-BATCA1
      CONNECTOR_CONFIGURATION_FORMAT_TKT_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_TKT: 30

      # XFSU
      CONNECTOR_CONFIGURATION_TOPIC_XFSU_DIR: xfsu
      CONNECTOR_CONFIGURATION_TOPIC_XFSU: emh-dev.ECARGO-FSU-JSON-UAT
      CONNECTOR_CONFIGURATION_FORMAT_XFSU_CLASS: io.confluent.connect.s3.format.json.JsonFormat

      CRAWLER_ROLE_NAME: ac-odh-event-streaming-services-msk-connect-res-batca1-catalog
    
    crtca1:
      DEBUG_MODE: true
      # AWS Account Id of EC2 Instance (PowerBI Windows Gateway)
      POWER_BI_AWS_ACCOUNT: ************

      # Kafka connector autoscaling capacity configs (we are not using this due to limitations)
      CAPACITY_AUTO_SCALING_MAX_WORKER_COUNT: 2
      CAPACITY_AUTO_SCALING_MCU_COUNT: 1
      CAPACITY_AUTO_SCALING_MIN_WORDER_COUNT: 1
      CAPACITY_AUTO_SCALING_SCALE_IN_POLICY_CPU_UTIL_PERCENTAGE: 20
      CAPACITY_AUTO_SCALING_SCALE_OUT_POLICY_CPU_UTIL_PERCENTAGE: 80

      # Kafka connector provisioned capacity configs
      CAPACITY_PROVISIONED_CAPACITY_WORKER_COUNT: 1
      CAPACITY_PROVISIONED_CAPACITY_MCU_COUNT: 1

      # common configs
      CONNECTOR_CONFIGURATION_FLUSH_SIZE: 1
      CONNECTOR_CONFIGURATION_BUCKET: ac-odh-stream-event-store-crtca1
      SERVICE_EXECUTION_ROLE_ARN: arn:aws:kafka:ca-central-1:************:cluster/digital-msk-crtca1/8180f3b2-f49f-4bc9-b062-63c73cfb18ac-4

      # Origin MKS Cluster
      ORIGIN_BOOTSTRAP_SERVER: b-3.digitalmskcrtca1.3xsgtp.c4.kafka.ca-central-1.amazonaws.com:9094,b-1.digitalmskcrtca1.3xsgtp.c4.kafka.ca-central-1.amazonaws.com:9094,b-2.digitalmskcrtca1.3xsgtp.c4.kafka.ca-central-1.amazonaws.com:9094
      ORIGIN_VPC_SG: sg-0fc9e26c21dbe202d
      ORIGIN_VPC_SUBNET_A: subnet-0e030f7de3818424f
      ORIGIN_VPC_SUBNET_B: subnet-0cfe753e530b47cc8
      ORIGIN_VPC_SUBNET_C: subnet-0f064c984d71076c7
      CUSTOM_PLUGIN_ARN: arn:aws:kafkaconnect:ca-central-1:************:custom-plugin/odh-raw-event-persister-plugin-central-1/8e3f8b85-c14f-41af-ae35-160768e0e787-4
      CUSTOM_PLUGIN_REVISION: 1

      # fsum
      CONNECTOR_CONFIGURATION_TOPIC_FSUM_DIR: fsum
      CONNECTOR_CONFIGURATION_TOPIC_FSUM: RAW-FSUM-LIDO-CRTCA1
      CONNECTOR_CONFIGURATION_FORMAT_FSUM_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_FSUM: 30

      # alg
      CONNECTOR_CONFIGURATION_TOPIC_ALG_DIR: alg
      CONNECTOR_CONFIGURATION_TOPIC_ALG: RAW-ALG-SMARTSUITE-CRTCA1
      CONNECTOR_CONFIGURATION_FORMAT_ALG_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_ALG: 30

      # aflvfl
      CONNECTOR_CONFIGURATION_TOPIC_AFLVFL_DIR: aflvfl
      CONNECTOR_CONFIGURATION_TOPIC_AFLVFL: RAW-AFLVFL-AIRCOM-CRTCA1
      CONNECTOR_CONFIGURATION_FORMAT_AFLVFL_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_AFLVFL: 30

      # pnr
      CONNECTOR_CONFIGURATION_TOPIC_PNR_DIR: pnr
      CONNECTOR_CONFIGURATION_TOPIC_PNR: RAW-PNR-ADH-CRTCA1
      CONNECTOR_CONFIGURATION_FORMAT_PNR_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_PNR: 30

      # csg
      CONNECTOR_CONFIGURATION_TOPIC_CSG_DIR: csg
      CONNECTOR_CONFIGURATION_TOPIC_CSG: RAW-CSG-SMARTSUITE-CRTCA1
      CONNECTOR_CONFIGURATION_FORMAT_CSG_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_CSG: 30

      # fdm
      CONNECTOR_CONFIGURATION_TOPIC_FDM_DIR: fdm
      CONNECTOR_CONFIGURATION_TOPIC_FDM: RAW-FDM-NLOPS-CRTCA1
      CONNECTOR_CONFIGURATION_FORMAT_FDM_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_FDM: 30

      # adm need to update with correct topic
      CONNECTOR_CONFIGURATION_TOPIC_ADM_DIR: adm
      CONNECTOR_CONFIGURATION_TOPIC_ADM: emh-dev.EAI-ADM-UAT
      CONNECTOR_CONFIGURATION_FORMAT_ADM_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_ADM: 30

      # ssm
      CONNECTOR_CONFIGURATION_TOPIC_SSM_DIR: ssm
      CONNECTOR_CONFIGURATION_TOPIC_SSM: RAW-SSM-SM-CRTCA1
      CONNECTOR_CONFIGURATION_FORMAT_SSM_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_SSM: 45

      # hotel accepted/declined
      CONNECTOR_CONFIGURATION_TOPIC_HA_DIR: hotel-accepted
      CONNECTOR_CONFIGURATION_TOPIC_HA: RAW-COMPENSATION-STORMX-CRTCA1
      CONNECTOR_CONFIGURATION_FORMAT_HA_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_HA: 30
      CRAWLER_TARGET_PATH: ac-odh-event-store-crtca1/hotel-accepted/RAW-COMPENSATION-STORMX-CRTCA1

      # meal voucher
      CONNECTOR_CONFIGURATION_TOPIC_MV_DIR: meal-voucher
      CONNECTOR_CONFIGURATION_TOPIC_MV: RAW-COMPENSATION-ICOUPONMEAL-CRTCA1
      CONNECTOR_CONFIGURATION_FORMAT_MV_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_MV: 30

      # account merge
      CONNECTOR_CONFIGURATION_TOPIC_AM_DIR: account-merge
      CONNECTOR_CONFIGURATION_TOPIC_AM: RAW-ACCOUNTMERGE-IFLY-CRTCA1
      CONNECTOR_CONFIGURATION_FORMAT_AM_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_AM: 30

      # account status
      CONNECTOR_CONFIGURATION_TOPIC_AS_DIR: account-status
      CONNECTOR_CONFIGURATION_TOPIC_AS: RAW-ACCOUNTSTATUS-IFLY-CRTCA1
      CONNECTOR_CONFIGURATION_FORMAT_AS_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_AS: 30

      # certificate status
      CONNECTOR_CONFIGURATION_TOPIC_CF_STATUS_DIR: certificate-status
      CONNECTOR_CONFIGURATION_TOPIC_CF_STATUS: RAW-CERTIFICATESTATUS-IFLY-CRTCA1
      CONNECTOR_CONFIGURATION_FORMAT_CF_STATUS_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_CF_STATUS: 30

      # cm feed
      CONNECTOR_CONFIGURATION_TOPIC_CM_FEED_DIR: cm-feed
      CONNECTOR_CONFIGURATION_TOPIC_CM_FEED: RAW-DCSPAX-ADH-CRTCA1
      CONNECTOR_CONFIGURATION_FORMAT_CM_FEED_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_CM_FEED: 30

      # cobrand card
      CONNECTOR_CONFIGURATION_TOPIC_CC_DIR: cobrand-card
      CONNECTOR_CONFIGURATION_TOPIC_CC: RAW-COBRANDCARD-IFLY-CRTCA1
      CONNECTOR_CONFIGURATION_FORMAT_CC_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_CC: 30

      # cp cl1a
      CONNECTOR_CONFIGURATION_TOPIC_CP_CL1A_DIR: cp-cl1a
      CONNECTOR_CONFIGURATION_TOPIC_CP_CL1A: RAW-CL1A-GIGYA-CRTCA1
      CONNECTOR_CONFIGURATION_FORMAT_CP_CL1A_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_CP_CL1A: 30

      # cp cl1b
      CONNECTOR_CONFIGURATION_TOPIC_CP_CL1B_DIR: cp-cl1b
      CONNECTOR_CONFIGURATION_TOPIC_CP_CL1B: RAW-CL1B-GIGYA-CRTCA1
      CONNECTOR_CONFIGURATION_FORMAT_CP_CL1B_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_CP_CL1B: 30

      # frozen stage
      CONNECTOR_CONFIGURATION_TOPIC_FROZEN_STAGE_DIR: frozen-stage
      CONNECTOR_CONFIGURATION_TOPIC_FROZEN_STAGE: RAW-FROZENSTAGE-IFLY-CRTCA1
      CONNECTOR_CONFIGURATION_FORMAT_FROZEN_STAGE_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_FROZEN_STAGE: 30

      # ifly tierstatus
      CONNECTOR_CONFIGURATION_TOPIC_TIERSTATUS_IFLY_DIR: ifly-tierstatus
      CONNECTOR_CONFIGURATION_TOPIC_TIERSTATUS_IFLY: RAW-TIERSTATUS-IFLY-CRTCA1
      CONNECTOR_CONFIGURATION_FORMAT_TIERSTATUS_IFLY_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_TIERSTATUS_IFLY: 30

      # ifly total bal
      CONNECTOR_CONFIGURATION_TOPIC_TOTAL_BAL_DIR: ifly-total-bal
      CONNECTOR_CONFIGURATION_TOPIC_TOTAL_BAL: RAW-TOTALBALANCE-IFLY-CRTCA1
      CONNECTOR_CONFIGURATION_FORMAT_TOTAL_BAL_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_TOTAL_BAL: 30

      # hotels catalog
      CONNECTOR_CONFIGURATION_TOPIC_HOTEL_CATALOG_DATA_DIR: hotel-catalog-data
      S3_EVENTS_EXPIRATION_DAYS_HOTEL_CATALOG_DATA: 30

      # jct roster output
      CONNECTOR_CONFIGURATION_TOPIC_JCT_ROSTER_OUTPUT_DIR: jct-roster-output
      CONNECTOR_CONFIGURATION_TOPIC_JCT_ROSTER_OUTPUT: emh-dev.JCT-CREWROSTER-UAT
      CONNECTOR_CONFIGURATION_FORMAT_JCT_ROSTER_OUTPUT_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_JCT_ROSTER_OUTPUT: 30

      #monthly block limit
      CONNECTOR_CONFIGURATION_TOPIC_MONTHLY_BLOCK_LIMIT_DIR: monthly-block-limit
      S3_EVENTS_EXPIRATION_DAYS_MONTHLY_BLOCK_LIMIT: 30

      # membership status
      CONNECTOR_CONFIGURATION_TOPIC_MEMBERSHIP_STATUS_DIR: membership-status
      CONNECTOR_CONFIGURATION_TOPIC_MEMBERSHIP_STATUS: RAW-MEMBERSHIPSTATUS-IFLY-CRTCA1
      CONNECTOR_CONFIGURATION_FORMAT_MEMBERSHIP_STATUS_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_MEMBERSHIP_STATUS: 30

      # pool
      CONNECTOR_CONFIGURATION_TOPIC_POOL_DIR: pool
      CONNECTOR_CONFIGURATION_TOPIC_POOL: RAW-POOL-IFLY-CRTCA1
      CONNECTOR_CONFIGURATION_FORMAT_POOL_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_POOL: 30

      # pool-balance
      CONNECTOR_CONFIGURATION_TOPIC_POOL_BALANCE_DIR: pool-balance
      CONNECTOR_CONFIGURATION_TOPIC_POOL_BALANCE: RAW-POOLBALANCE-IFLY-CRTCA1
      CONNECTOR_CONFIGURATION_FORMAT_POOL_BALANCE_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_POOL_BALANCE: 30

      # pool-permission
      CONNECTOR_CONFIGURATION_TOPIC_POOL_PERMISSION_DIR: pool-permission
      CONNECTOR_CONFIGURATION_TOPIC_POOL_PERMISSION: RAW-POOLPERMISSION-IFLY-CRTCA1
      CONNECTOR_CONFIGURATION_FORMAT_POOL_PERMISSION_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_POOL_PERMISSION: 30

      # pool-redemption
      CONNECTOR_CONFIGURATION_TOPIC_POOL_REDEMPTION_DIR: pool-redemption
      CONNECTOR_CONFIGURATION_TOPIC_POOL_REDEMPTION: RAW-POOLREDEMPTION-IFLY-CRTCA1
      CONNECTOR_CONFIGURATION_FORMAT_POOL_REDEMPTION_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_POOL_REDEMPTION: 30

      # profile
      CONNECTOR_CONFIGURATION_TOPIC_PROFILE_DIR: profile
      CONNECTOR_CONFIGURATION_TOPIC_PROFILE: RAW-PROFILE-IFLY-CRTCA1
      CONNECTOR_CONFIGURATION_FORMAT_PROFILE_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_PROFILE: 30

      # retail-partnership
      CONNECTOR_CONFIGURATION_TOPIC_RETAIL_PARTNERSHIP_DIR: retail-partnership
      CONNECTOR_CONFIGURATION_TOPIC_RETAIL_PARTNERSHIP: RAW-RETAILPARTNERSHIP-IFLY-CRTCA1
      CONNECTOR_CONFIGURATION_FORMAT_RETAIL_PARTNERSHIP_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_RETAIL_PARTNERSHIP: 30

      # retroclaim
      CONNECTOR_CONFIGURATION_TOPIC_RETROCLAIM_DIR: retroclaim
      CONNECTOR_CONFIGURATION_TOPIC_RETROCLAIM: RAW-RETROCLAIM-IFLY-CRTCA1
      CONNECTOR_CONFIGURATION_FORMAT_RETROCLAIM_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_RETROCLAIM: 30

      # smartload
      CONNECTOR_CONFIGURATION_TOPIC_SMARTLOAD_DIR: smartload
      CONNECTOR_CONFIGURATION_TOPIC_SMARTLOAD: RAW-WAREHOUSEDATA-S4A-SMTLD-CRTCA1
      CONNECTOR_CONFIGURATION_FORMAT_SMARTLOAD_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_SMARTLOAD: 30

      # ccm-aes
      CONNECTOR_CONFIGURATION_TOPIC_CCM_AES_DIR: ccm-aes
      CONNECTOR_CONFIGURATION_TOPIC_CCM_AES: RAW-AES-CC-CRTCA1
      CONNECTOR_CONFIGURATION_FORMAT_CCM_AES_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_CCM_AES: 30

      # ccm-ctr
      CONNECTOR_CONFIGURATION_TOPIC_CCM_CTR_DIR: ccm-ctr
      CONNECTOR_CONFIGURATION_TOPIC_CCM_CTR: RAW-CTR-CC-CRTCA1
      CONNECTOR_CONFIGURATION_FORMAT_CCM_CTR_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_CCM_CTR: 30

      # hotel cancellation
      CONNECTOR_CONFIGURATION_TOPIC_HC_DIR: hotel-cancellation
      CONNECTOR_CONFIGURATION_TOPIC_HC: RAW-COMPENSATION-DBAASHOTEL-CRTCA1
      CONNECTOR_CONFIGURATION_FORMAT_HC_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_HC: 30

      # transportation voucher
      CONNECTOR_CONFIGURATION_TOPIC_TV_DIR: transportation-voucher
      CONNECTOR_CONFIGURATION_TOPIC_TV: RAW-COMPENSATION-ICOUPONTRANSPORTATION-CRTCA1
      CONNECTOR_CONFIGURATION_FORMAT_TV_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_TV: 30

      # Flight Tracking
      CONNECTOR_CONFIGURATION_TOPIC_FLIGHT_EVENT_FLIGHTAWARE_DIR: flight-event-flightaware
      CONNECTOR_CONFIGURATION_TOPIC_FLIGHT_EVENT_FLIGHTAWARE: emh-dev.MULE-FLIGHTAWARE-FLIGHTEVENTS-DEV
      CONNECTOR_CONFIGURATION_FORMAT_FLIGHT_EVENT_FLIGHTAWARE_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_FLIGHT_EVENT_FLIGHTAWARE: 30

      CONNECTOR_CONFIGURATION_TOPIC_WEATHER_EVENT_FLIGHTAWARE_DIR: weather-event-flightaware
      CONNECTOR_CONFIGURATION_TOPIC_WEATHER_EVENT_FLIGHTAWARE: emh-dev.MULE-FLIGHTAWARE-WEATHER-DEV
      CONNECTOR_CONFIGURATION_FORMAT_WEATHER_EVENT_FLIGHTAWARE_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_WEATHER_EVENT_FLIGHTAWARE: 30

      CONNECTOR_CONFIGURATION_TOPIC_FLIGHT_EVENT_AR24_DIR: flight-event-ar24
      CONNECTOR_CONFIGURATION_TOPIC_FLIGHT_EVENT_AR24: emh-dev.MULE-FLIGHTAWARE-FR24-DEV
      CONNECTOR_CONFIGURATION_FORMAT_FLIGHT_EVENT_AR24_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_FLIGHT_EVENT_AR24: 30

      # fraud screening
      CONNECTOR_CONFIGURATION_TOPIC_FRAUD_SCREENING_DIR: fraud-screening
      CONNECTOR_CONFIGURATION_TOPIC_FRAUD_SCREENING: emh-dev.MULE-XPAY-PAYMENTSTATUS-DEV
      CONNECTOR_CONFIGURATION_FORMAT_FRAUD_SCREENING_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_FRAUD_SCREENING: 30

      # tkt
      CONNECTOR_CONFIGURATION_TOPIC_TKT_DIR: tkt
      CONNECTOR_CONFIGURATION_TOPIC_TKT: RAW-TKT-CRTCA1
      CONNECTOR_CONFIGURATION_FORMAT_TKT_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_TKT: 30

      # XFSU
      CONNECTOR_CONFIGURATION_TOPIC_XFSU_DIR: xfsu
      CONNECTOR_CONFIGURATION_TOPIC_XFSU: emh-dev.ECARGO-FSU-JSON-UAT
      CONNECTOR_CONFIGURATION_FORMAT_XFSU_CLASS: io.confluent.connect.s3.format.json.JsonFormat

      CRAWLER_ROLE_NAME: ac-odh-event-streaming-services-msk-connect-res-crtca1-catalog
    
    preprodca1:
      DEBUG_MODE: true
      # AWS Account Id of EC2 Instance (PowerBI Windows Gateway)
      POWER_BI_AWS_ACCOUNT: ************

      # Kafka connector autoscaling capacity configs
      CAPACITY_AUTO_SCALING_MAX_WORKER_COUNT: 2
      CAPACITY_AUTO_SCALING_MCU_COUNT: 1
      CAPACITY_AUTO_SCALING_MIN_WORDER_COUNT: 1
      CAPACITY_AUTO_SCALING_SCALE_IN_POLICY_CPU_UTIL_PERCENTAGE: 20
      CAPACITY_AUTO_SCALING_SCALE_OUT_POLICY_CPU_UTIL_PERCENTAGE: 80

      # Kafka connector provisioned capacity configs
      CAPACITY_PROVISIONED_CAPACITY_WORKER_COUNT: 1
      CAPACITY_PROVISIONED_CAPACITY_MCU_COUNT: 1

      # common configs
      CONNECTOR_CONFIGURATION_FLUSH_SIZE: 1
      CONNECTOR_CONFIGURATION_BUCKET: ac-odh-stream-event-store-preprodca1
      SERVICE_EXECUTION_ROLE_ARN: arn:aws:kafka:ca-central-1:************:cluster/digital-msk-preprodca1/1a4dd74b-ff0e-4edb-8371-7a480a5ac461-3

      # Origin MKS Cluster
      ORIGIN_BOOTSTRAP_SERVER: b-3.digitalmskpreprodca.of6xqs.c3.kafka.ca-central-1.amazonaws.com:9094,b-1.digitalmskpreprodca.of6xqs.c3.kafka.ca-central-1.amazonaws.com:9094,b-2.digitalmskpreprodca.of6xqs.c3.kafka.ca-central-1.amazonaws.com:9094
      ORIGIN_VPC_SG: sg-010736e82750fa03c
      ORIGIN_VPC_SUBNET_A: subnet-0ce0a9dbac4cfc597
      ORIGIN_VPC_SUBNET_B: subnet-06dd18a1cfb259360
      ORIGIN_VPC_SUBNET_C: subnet-0e01bb22ef83c9ca3
      CUSTOM_PLUGIN_ARN: arn:aws:kafkaconnect:ca-central-1:************:custom-plugin/odh-raw-event-persister-plugin-central-1/5071f7cd-232e-49e2-acee-9af9484ab86f-3
      CUSTOM_PLUGIN_REVISION: 1

      # fsum
      CONNECTOR_CONFIGURATION_TOPIC_FSUM_DIR: fsum
      CONNECTOR_CONFIGURATION_TOPIC_FSUM: RAW-FSUM-LIDO-PREPRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_FSUM_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_FSUM: 30

      # alg
      CONNECTOR_CONFIGURATION_TOPIC_ALG_DIR: alg
      CONNECTOR_CONFIGURATION_TOPIC_ALG: RAW-ALG-SMARTSUITE-PREPRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_ALG_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_ALG: 30

      # aflvfl
      CONNECTOR_CONFIGURATION_TOPIC_AFLVFL_DIR: aflvfl
      CONNECTOR_CONFIGURATION_TOPIC_AFLVFL: RAW-AFLVFL-AIRCOM-PREPRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_AFLVFL_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_AFLVFL: 30

      # pnr
      CONNECTOR_CONFIGURATION_TOPIC_PNR_DIR: pnr
      CONNECTOR_CONFIGURATION_TOPIC_PNR: RAW-PNR-ADH-PREPRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_PNR_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_PNR: 30

      # csg
      CONNECTOR_CONFIGURATION_TOPIC_CSG_DIR: csg
      CONNECTOR_CONFIGURATION_TOPIC_CSG: RAW-CSG-SMARTSUITE-PREPRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_CSG_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_CSG: 30

      # fdm
      CONNECTOR_CONFIGURATION_TOPIC_FDM_DIR: fdm
      CONNECTOR_CONFIGURATION_TOPIC_FDM: RAW-FDM-NLOPS-PREPRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_FDM_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_FDM: 30

      # adm
      CONNECTOR_CONFIGURATION_TOPIC_ADM_DIR: adm
      CONNECTOR_CONFIGURATION_TOPIC_ADM: emh-dev.EAI-ADM-UAT
      CONNECTOR_CONFIGURATION_FORMAT_ADM_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_ADM: 30

      # ssm
      CONNECTOR_CONFIGURATION_TOPIC_SSM_DIR: ssm
      CONNECTOR_CONFIGURATION_TOPIC_SSM: RAW-SSM-SM-PREPRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_SSM_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_SSM: 45

      # hotel accepted/declined
      CONNECTOR_CONFIGURATION_TOPIC_HA_DIR: hotel-accepted
      CONNECTOR_CONFIGURATION_TOPIC_HA: RAW-COMPENSATION-STORMX-PREPRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_HA_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_HA: 30
      CRAWLER_TARGET_PATH: ac-odh-event-store-preprodca1/hotel-accepted/RAW-COMPENSATION-STORMX-PREPRODCA1

      # meal voucher
      CONNECTOR_CONFIGURATION_TOPIC_MV_DIR: meal-voucher
      CONNECTOR_CONFIGURATION_TOPIC_MV: RAW-COMPENSATION-ICOUPONMEAL-PREPRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_MV_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_MV: 30

      # account merge
      CONNECTOR_CONFIGURATION_TOPIC_AM_DIR: account-merge
      CONNECTOR_CONFIGURATION_TOPIC_AM: RAW-ACCOUNTMERGE-IFLY-PREPRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_AM_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_AM: 30

      # account status
      CONNECTOR_CONFIGURATION_TOPIC_AS_DIR: account-status
      CONNECTOR_CONFIGURATION_TOPIC_AS: RAW-ACCOUNTSTATUS-IFLY-PREPRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_AS_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_AS: 30

      # certificate status
      CONNECTOR_CONFIGURATION_TOPIC_CF_STATUS_DIR: certificate-status
      CONNECTOR_CONFIGURATION_TOPIC_CF_STATUS: RAW-CERTIFICATESTATUS-IFLY-PREPRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_CF_STATUS_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_CF_STATUS: 30

      # cm feed
      CONNECTOR_CONFIGURATION_TOPIC_CM_FEED_DIR: cm-feed
      CONNECTOR_CONFIGURATION_TOPIC_CM_FEED: RAW-DCSPAX-ADH-PREPRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_CM_FEED_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_CM_FEED: 30

      # cobrand card
      CONNECTOR_CONFIGURATION_TOPIC_CC_DIR: cobrand-card
      CONNECTOR_CONFIGURATION_TOPIC_CC: RAW-COBRANDCARD-IFLY-PREPRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_CC_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_CC: 30

      # cp cl1a
      CONNECTOR_CONFIGURATION_TOPIC_CP_CL1A_DIR: cp-cl1a
      CONNECTOR_CONFIGURATION_TOPIC_CP_CL1A: RAW-CL1A-GIGYA-PREPRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_CP_CL1A_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_CP_CL1A: 30

      # cp cl1b
      CONNECTOR_CONFIGURATION_TOPIC_CP_CL1B_DIR: cp-cl1b
      CONNECTOR_CONFIGURATION_TOPIC_CP_CL1B: RAW-CL1B-GIGYA-PREPRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_CP_CL1B_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_CP_CL1B: 30

      # frozen stage
      CONNECTOR_CONFIGURATION_TOPIC_FROZEN_STAGE_DIR: frozen-stage
      CONNECTOR_CONFIGURATION_TOPIC_FROZEN_STAGE: RAW-FROZENSTAGE-IFLY-PREPRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_FROZEN_STAGE_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_FROZEN_STAGE: 30

      # ifly tierstatus
      CONNECTOR_CONFIGURATION_TOPIC_TIERSTATUS_IFLY_DIR: ifly-tierstatus
      CONNECTOR_CONFIGURATION_TOPIC_TIERSTATUS_IFLY: RAW-TIERSTATUS-IFLY-PREPRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_TIERSTATUS_IFLY_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_TIERSTATUS_IFLY: 30

      # ifly total bal
      CONNECTOR_CONFIGURATION_TOPIC_TOTAL_BAL_DIR: ifly-total-bal
      CONNECTOR_CONFIGURATION_TOPIC_TOTAL_BAL: RAW-TOTALBALANCE-IFLY-PREPRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_TOTAL_BAL_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_TOTAL_BAL: 30

      # membership status
      CONNECTOR_CONFIGURATION_TOPIC_MEMBERSHIP_STATUS_DIR: membership-status
      CONNECTOR_CONFIGURATION_TOPIC_MEMBERSHIP_STATUS: RAW-MEMBERSHIPSTATUS-IFLY-PREPRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_MEMBERSHIP_STATUS_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_MEMBERSHIP_STATUS: 30

      # retail-partnership
      CONNECTOR_CONFIGURATION_TOPIC_RETAIL_PARTNERSHIP_DIR: retail-partnership
      CONNECTOR_CONFIGURATION_TOPIC_RETAIL_PARTNERSHIP: RAW-RETAILPARTNERSHIP-IFLY-PREPRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_RETAIL_PARTNERSHIP_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_RETAIL_PARTNERSHIP: 30

      # smartload
      CONNECTOR_CONFIGURATION_TOPIC_SMARTLOAD_DIR: smartload
      CONNECTOR_CONFIGURATION_TOPIC_SMARTLOAD: RAW-WAREHOUSEDATA-S4A-SMTLD-PREPRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_SMARTLOAD_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_SMARTLOAD: 30

      # ccm-aes
      CONNECTOR_CONFIGURATION_TOPIC_CCM_AES_DIR: ccm-aes
      CONNECTOR_CONFIGURATION_TOPIC_CCM_AES: RAW-AES-CC-PREPRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_CCM_AES_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_CCM_AES: 30

      # ccm-ctr
      CONNECTOR_CONFIGURATION_TOPIC_CCM_CTR_DIR: ccm-ctr
      CONNECTOR_CONFIGURATION_TOPIC_CCM_CTR: RAW-CTR-CC-PREPRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_CCM_CTR_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_CCM_CTR: 30

      # hotel cancellation
      CONNECTOR_CONFIGURATION_TOPIC_HC_DIR: hotel-cancellation
      CONNECTOR_CONFIGURATION_TOPIC_HC: RAW-HOTELCANCELLATION-STORMX-PREPRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_HC_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_HC: 30

      # pool
      CONNECTOR_CONFIGURATION_TOPIC_POOL_DIR: pool
      CONNECTOR_CONFIGURATION_TOPIC_POOL: RAW-POOL-IFLY-PREPRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_POOL_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_POOL: 30

      # pool-balance
      CONNECTOR_CONFIGURATION_TOPIC_POOL_BALANCE_DIR: pool-balance
      CONNECTOR_CONFIGURATION_TOPIC_POOL_BALANCE: RAW-POOLBALANCE-IFLY-PREPRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_POOL_BALANCE_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_POOL_BALANCE: 30

      # pool-permission
      CONNECTOR_CONFIGURATION_TOPIC_POOL_PERMISSION_DIR: pool-permission
      CONNECTOR_CONFIGURATION_TOPIC_POOL_PERMISSION: RAW-POOLPERMISSION-IFLY-PREPRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_POOL_PERMISSION_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_POOL_PERMISSION: 30

      # pool-redemption
      CONNECTOR_CONFIGURATION_TOPIC_POOL_REDEMPTION_DIR: pool-redemption
      CONNECTOR_CONFIGURATION_TOPIC_POOL_REDEMPTION: RAW-POOLREDEMPTION-IFLY-PREPRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_POOL_REDEMPTION_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_POOL_REDEMPTION: 30

      # retroclaim
      CONNECTOR_CONFIGURATION_TOPIC_RETROCLAIM_DIR: retroclaim
      CONNECTOR_CONFIGURATION_TOPIC_RETROCLAIM: RAW-RETROCLAIM-IFLY-PREPRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_RETROCLAIM_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_RETROCLAIM: 30

      # profile
      CONNECTOR_CONFIGURATION_TOPIC_PROFILE_DIR: profile
      CONNECTOR_CONFIGURATION_TOPIC_PROFILE: RAW-PROFILE-IFLY-PREPRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_PROFILE_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_PROFILE: 30

      # transportation voucher
      CONNECTOR_CONFIGURATION_TOPIC_TV_DIR: transportation-voucher
      CONNECTOR_CONFIGURATION_TOPIC_TV: RAW-COMPENSATION-ICOUPONTRANSPORTATION-PREPRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_TV_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_TV: 30

      # Flight Tracking
      CONNECTOR_CONFIGURATION_TOPIC_FLIGHT_EVENT_FLIGHTAWARE_DIR: flight-event-flightaware
      CONNECTOR_CONFIGURATION_TOPIC_FLIGHT_EVENT_FLIGHTAWARE: msk-ca-region.MULE-FLIGHTAWARE-FLIGHTEVENTS-PRD
      CONNECTOR_CONFIGURATION_FORMAT_FLIGHT_EVENT_FLIGHTAWARE_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_FLIGHT_EVENT_FLIGHTAWARE: 30

      CONNECTOR_CONFIGURATION_TOPIC_WEATHER_EVENT_FLIGHTAWARE_DIR: weather-event-flightaware
      CONNECTOR_CONFIGURATION_TOPIC_WEATHER_EVENT_FLIGHTAWARE: msk-ca-region.MULE-FLIGHTAWARE-WEATHER-PRD
      CONNECTOR_CONFIGURATION_FORMAT_WEATHER_EVENT_FLIGHTAWARE_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_WEATHER_EVENT_FLIGHTAWARE: 30

      CONNECTOR_CONFIGURATION_TOPIC_FLIGHT_EVENT_AR24_DIR: flight-event-ar24
      CONNECTOR_CONFIGURATION_TOPIC_FLIGHT_EVENT_AR24: msk-ca-region.MULE-FLIGHTAWARE-FR24-PRD
      CONNECTOR_CONFIGURATION_FORMAT_FLIGHT_EVENT_AR24_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_FLIGHT_EVENT_AR24: 30

      # fraud screening
      CONNECTOR_CONFIGURATION_TOPIC_FRAUD_SCREENING_DIR: fraud-screening
      CONNECTOR_CONFIGURATION_TOPIC_FRAUD_SCREENING: msk-ca-region.MULE-XPAY-PAYMENTSTATUS-PRD
      CONNECTOR_CONFIGURATION_FORMAT_FRAUD_SCREENING_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_FRAUD_SCREENING: 30

      # tkt
      CONNECTOR_CONFIGURATION_TOPIC_TKT_DIR: tkt
      CONNECTOR_CONFIGURATION_TOPIC_TKT: RAW-TKT-PREPRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_TKT_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_TKT: 30

      CRAWLER_ROLE_NAME: ac-odh-event-streaming-services-msk-connect-res-preca1-catalog

      # jct roster output
      CONNECTOR_CONFIGURATION_TOPIC_JCT_ROSTER_OUTPUT_DIR: jct-roster-output
      CONNECTOR_CONFIGURATION_TOPIC_JCT_ROSTER_OUTPUT: RAW-ROSTER-JCT-PREPRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_JCT_ROSTER_OUTPUT_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_JCT_ROSTER_OUTPUT: 30

      # hotels catalog
      CONNECTOR_CONFIGURATION_TOPIC_HOTEL_CATALOG_DATA_DIR: hotel-catalog-data
      S3_EVENTS_EXPIRATION_DAYS_HOTEL_CATALOG_DATA: 30

      #monthly block limit
      CONNECTOR_CONFIGURATION_TOPIC_MONTHLY_BLOCK_LIMIT_DIR: monthly-block-limit
      S3_EVENTS_EXPIRATION_DAYS_MONTHLY_BLOCK_LIMIT: 30

    prodca1:
      DEBUG_MODE: true
      # AWS Account Id of EC2 Instance (PowerBI Windows Gateway)
      POWER_BI_AWS_ACCOUNT: ************

      # Kafka connector autoscaling capacity configs (we are not using this due to limitations)
      CAPACITY_AUTO_SCALING_MAX_WORKER_COUNT: 2
      CAPACITY_AUTO_SCALING_MCU_COUNT: 1
      CAPACITY_AUTO_SCALING_MIN_WORDER_COUNT: 1
      CAPACITY_AUTO_SCALING_SCALE_IN_POLICY_CPU_UTIL_PERCENTAGE: 20
      CAPACITY_AUTO_SCALING_SCALE_OUT_POLICY_CPU_UTIL_PERCENTAGE: 80

      # Kafka connector provisioned capacity configs
      CAPACITY_PROVISIONED_CAPACITY_WORKER_COUNT: 1
      CAPACITY_PROVISIONED_CAPACITY_MCU_COUNT: 1

      # common configs
      CONNECTOR_CONFIGURATION_FLUSH_SIZE: 1
      CONNECTOR_CONFIGURATION_BUCKET: ac-odh-stream-event-store-prodca1
      SERVICE_EXECUTION_ROLE_ARN: arn:aws:kafka:ca-central-1:************:cluster/digital-msk-prodca1/602a1803-fd95-4a17-b74b-7c6781c4d09c-4

      # Origin MKS Cluster
      ORIGIN_BOOTSTRAP_SERVER: b-1.digitalmskprodca1.scrvp3.c4.kafka.ca-central-1.amazonaws.com:9094,b-3.digitalmskprodca1.scrvp3.c4.kafka.ca-central-1.amazonaws.com:9094,b-2.digitalmskprodca1.scrvp3.c4.kafka.ca-central-1.amazonaws.com:9094
      ORIGIN_VPC_SG: sg-0c784bd5bb98bf583
      ORIGIN_VPC_SUBNET_A: subnet-0e21904f481f055d1
      ORIGIN_VPC_SUBNET_B: subnet-075c6590cab8b8957
      ORIGIN_VPC_SUBNET_C: subnet-0895b4b087142e29f
      CUSTOM_PLUGIN_ARN: arn:aws:kafkaconnect:ca-central-1:************:custom-plugin/odh-raw-event-persister-plugin-central-1/a15eb7ea-5b62-4890-87e6-c83bf2ec5cd1-4
      CUSTOM_PLUGIN_REVISION: 1

      # fsum
      CONNECTOR_CONFIGURATION_TOPIC_FSUM_DIR: fsum
      CONNECTOR_CONFIGURATION_TOPIC_FSUM: RAW-FSUM-LIDO-PRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_FSUM_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_FSUM: 30

      # alg
      CONNECTOR_CONFIGURATION_TOPIC_ALG_DIR: alg
      CONNECTOR_CONFIGURATION_TOPIC_ALG: RAW-ALG-SMARTSUITE-PRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_ALG_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_ALG: 30

      # aflvfl
      CONNECTOR_CONFIGURATION_TOPIC_AFLVFL_DIR: aflvfl
      CONNECTOR_CONFIGURATION_TOPIC_AFLVFL: RAW-AFLVFL-AIRCOM-PRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_AFLVFL_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_AFLVFL: 30

      # pnr
      CONNECTOR_CONFIGURATION_TOPIC_PNR_DIR: pnr
      CONNECTOR_CONFIGURATION_TOPIC_PNR: RAW-PNR-ADH-PRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_PNR_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_PNR: 30

      # csg
      CONNECTOR_CONFIGURATION_TOPIC_CSG_DIR: csg
      CONNECTOR_CONFIGURATION_TOPIC_CSG: RAW-CSG-SMARTSUITE-PRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_CSG_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_CSG: 30

      # fdm
      CONNECTOR_CONFIGURATION_TOPIC_FDM_DIR: fdm
      CONNECTOR_CONFIGURATION_TOPIC_FDM: RAW-FDM-NLOPS-PRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_FDM_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_FDM: 30

      # adm need to update with the correct topic
      CONNECTOR_CONFIGURATION_TOPIC_ADM_DIR: adm
      CONNECTOR_CONFIGURATION_TOPIC_ADM: emh-dev.EAI-ADM-UAT
      CONNECTOR_CONFIGURATION_FORMAT_ADM_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_ADM: 30

      # ssm
      CONNECTOR_CONFIGURATION_TOPIC_SSM_DIR: ssm
      CONNECTOR_CONFIGURATION_TOPIC_SSM: RAW-SSM-SM-PRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_SSM_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_SSM: 45

      # hotel accepted/declined
      CONNECTOR_CONFIGURATION_TOPIC_HA_DIR: hotel-accepted
      CONNECTOR_CONFIGURATION_TOPIC_HA: RAW-COMPENSATION-STORMX-PRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_HA_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_HA: 30
      CRAWLER_TARGET_PATH: ac-odh-event-store-prodca1/hotel-accepted/RAW-COMPENSATION-STORMX-PRODCA1

      # meal voucher
      CONNECTOR_CONFIGURATION_TOPIC_MV_DIR: meal-voucher
      CONNECTOR_CONFIGURATION_TOPIC_MV: RAW-COMPENSATION-ICOUPONMEAL-PRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_MV_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_MV: 30

      # account merge
      CONNECTOR_CONFIGURATION_TOPIC_AM_DIR: account-merge
      CONNECTOR_CONFIGURATION_TOPIC_AM: RAW-ACCOUNTMERGE-IFLY-PRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_AM_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_AM: 30

      # account status
      CONNECTOR_CONFIGURATION_TOPIC_AS_DIR: account-status
      CONNECTOR_CONFIGURATION_TOPIC_AS: RAW-ACCOUNTSTATUS-IFLY-PRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_AS_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_AS: 30

      # certificate status
      CONNECTOR_CONFIGURATION_TOPIC_CF_STATUS_DIR: certificate-status
      CONNECTOR_CONFIGURATION_TOPIC_CF_STATUS: RAW-CERTIFICATESTATUS-IFLY-PRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_CF_STATUS_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_CF_STATUS: 30

      # cm feed
      CONNECTOR_CONFIGURATION_TOPIC_CM_FEED_DIR: cm-feed
      CONNECTOR_CONFIGURATION_TOPIC_CM_FEED: RAW-DCSPAX-ADH-PRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_CM_FEED_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_CM_FEED: 30

      # cobrand card
      CONNECTOR_CONFIGURATION_TOPIC_CC_DIR: cobrand-card
      CONNECTOR_CONFIGURATION_TOPIC_CC: RAW-COBRANDCARD-IFLY-PRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_CC_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_CC: 30

      # cp cl1a
      CONNECTOR_CONFIGURATION_TOPIC_CP_CL1A_DIR: cp-cl1a
      CONNECTOR_CONFIGURATION_TOPIC_CP_CL1A: RAW-CL1A-GIGYA-PRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_CP_CL1A_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_CP_CL1A: 30

      # cp cl1b
      CONNECTOR_CONFIGURATION_TOPIC_CP_CL1B_DIR: cp-cl1b
      CONNECTOR_CONFIGURATION_TOPIC_CP_CL1B: RAW-CL1B-GIGYA-PRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_CP_CL1B_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_CP_CL1B: 30

      # frozen stage
      CONNECTOR_CONFIGURATION_TOPIC_FROZEN_STAGE_DIR: frozen-stage
      CONNECTOR_CONFIGURATION_TOPIC_FROZEN_STAGE: RAW-FROZENSTAGE-IFLY-PRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_FROZEN_STAGE_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_FROZEN_STAGE: 30

      # ifly tierstatus
      CONNECTOR_CONFIGURATION_TOPIC_TIERSTATUS_IFLY_DIR: ifly-tierstatus
      CONNECTOR_CONFIGURATION_TOPIC_TIERSTATUS_IFLY: RAW-TIERSTATUS-IFLY-PRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_TIERSTATUS_IFLY_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_TIERSTATUS_IFLY: 30

      # ifly total bal
      CONNECTOR_CONFIGURATION_TOPIC_TOTAL_BAL_DIR: ifly-total-bal
      CONNECTOR_CONFIGURATION_TOPIC_TOTAL_BAL: RAW-TOTALBALANCE-IFLY-PRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_TOTAL_BAL_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_TOTAL_BAL: 30

      # hotel catalog data
      CONNECTOR_CONFIGURATION_TOPIC_HOTEL_CATALOG_DATA_DIR: hotel-catalog-data
      S3_EVENTS_EXPIRATION_DAYS_HOTEL_CATALOG_DATA: 30

      # jct roster output
      CONNECTOR_CONFIGURATION_TOPIC_JCT_ROSTER_OUTPUT_DIR: jct-roster-output
      CONNECTOR_CONFIGURATION_TOPIC_JCT_ROSTER_OUTPUT: RAW-ROSTER-JCT-PRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_JCT_ROSTER_OUTPUT_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_JCT_ROSTER_OUTPUT: 30

      #monthly block limit
      CONNECTOR_CONFIGURATION_TOPIC_MONTHLY_BLOCK_LIMIT_DIR: monthly-block-limit
      S3_EVENTS_EXPIRATION_DAYS_MONTHLY_BLOCK_LIMIT: 30

      # membership status
      CONNECTOR_CONFIGURATION_TOPIC_MEMBERSHIP_STATUS_DIR: membership-status
      CONNECTOR_CONFIGURATION_TOPIC_MEMBERSHIP_STATUS: RAW-MEMBERSHIPSTATUS-IFLY-PRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_MEMBERSHIP_STATUS_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_MEMBERSHIP_STATUS: 30

      # pool
      CONNECTOR_CONFIGURATION_TOPIC_POOL_DIR: pool
      CONNECTOR_CONFIGURATION_TOPIC_POOL: RAW-POOL-IFLY-PRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_POOL_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_POOL: 30

      # pool-balance
      CONNECTOR_CONFIGURATION_TOPIC_POOL_BALANCE_DIR: pool-balance
      CONNECTOR_CONFIGURATION_TOPIC_POOL_BALANCE: RAW-POOLBALANCE-IFLY-PRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_POOL_BALANCE_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_POOL_BALANCE: 30

      # pool-permission
      CONNECTOR_CONFIGURATION_TOPIC_POOL_PERMISSION_DIR: pool-permission
      CONNECTOR_CONFIGURATION_TOPIC_POOL_PERMISSION: RAW-POOLPERMISSION-IFLY-PRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_POOL_PERMISSION_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_POOL_PERMISSION: 30

      # pool-redemption
      CONNECTOR_CONFIGURATION_TOPIC_POOL_REDEMPTION_DIR: pool-redemption
      CONNECTOR_CONFIGURATION_TOPIC_POOL_REDEMPTION: RAW-POOLREDEMPTION-IFLY-PRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_POOL_REDEMPTION_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_POOL_REDEMPTION: 30

      # profile
      CONNECTOR_CONFIGURATION_TOPIC_PROFILE_DIR: profile
      CONNECTOR_CONFIGURATION_TOPIC_PROFILE: RAW-PROFILE-IFLY-PRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_PROFILE_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_PROFILE: 30

      # retail-partnership
      CONNECTOR_CONFIGURATION_TOPIC_RETAIL_PARTNERSHIP_DIR: retail-partnership
      CONNECTOR_CONFIGURATION_TOPIC_RETAIL_PARTNERSHIP: RAW-RETAILPARTNERSHIP-IFLY-PRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_RETAIL_PARTNERSHIP_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_RETAIL_PARTNERSHIP: 30

      # retroclaim
      CONNECTOR_CONFIGURATION_TOPIC_RETROCLAIM_DIR: retroclaim
      CONNECTOR_CONFIGURATION_TOPIC_RETROCLAIM: RAW-RETROCLAIM-IFLY-PRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_RETROCLAIM_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_RETROCLAIM: 30

      # smartload
      CONNECTOR_CONFIGURATION_TOPIC_SMARTLOAD_DIR: smartload
      CONNECTOR_CONFIGURATION_TOPIC_SMARTLOAD: RAW-WAREHOUSEDATA-S4A-SMTLD-PRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_SMARTLOAD_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_SMARTLOAD: 30

      # ccm-aes
      CONNECTOR_CONFIGURATION_TOPIC_CCM_AES_DIR: ccm-aes
      CONNECTOR_CONFIGURATION_TOPIC_CCM_AES: RAW-AES-CC-PRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_CCM_AES_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_CCM_AES: 30

      # ccm-ctr
      CONNECTOR_CONFIGURATION_TOPIC_CCM_CTR_DIR: ccm-ctr
      CONNECTOR_CONFIGURATION_TOPIC_CCM_CTR: RAW-CTR-CC-PRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_CCM_CTR_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_CCM_CTR: 30

      # hotel cancellation
      CONNECTOR_CONFIGURATION_TOPIC_HC_DIR: hotel-cancellation
      CONNECTOR_CONFIGURATION_TOPIC_HC: RAW-COMPENSATION-DBAASHOTEL-PRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_HC_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_HC: 30

      # transportation voucher
      CONNECTOR_CONFIGURATION_TOPIC_TV_DIR: transportation-voucher
      CONNECTOR_CONFIGURATION_TOPIC_TV: RAW-COMPENSATION-ICOUPONTRANSPORTATION-PRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_TV_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_TV: 30

      # Flight Tracking
      CONNECTOR_CONFIGURATION_TOPIC_FLIGHT_EVENT_FLIGHTAWARE_DIR: flight-event-flightaware
      CONNECTOR_CONFIGURATION_TOPIC_FLIGHT_EVENT_FLIGHTAWARE: msk-ca-region.MULE-FLIGHTAWARE-FLIGHTEVENTS-PRD
      CONNECTOR_CONFIGURATION_FORMAT_FLIGHT_EVENT_FLIGHTAWARE_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_FLIGHT_EVENT_FLIGHTAWARE: 30

      CONNECTOR_CONFIGURATION_TOPIC_WEATHER_EVENT_FLIGHTAWARE_DIR: weather-event-flightaware
      CONNECTOR_CONFIGURATION_TOPIC_WEATHER_EVENT_FLIGHTAWARE: msk-ca-region.MULE-FLIGHTAWARE-WEATHER-PRD
      CONNECTOR_CONFIGURATION_FORMAT_WEATHER_EVENT_FLIGHTAWARE_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_WEATHER_EVENT_FLIGHTAWARE: 30

      CONNECTOR_CONFIGURATION_TOPIC_FLIGHT_EVENT_AR24_DIR: flight-event-ar24
      CONNECTOR_CONFIGURATION_TOPIC_FLIGHT_EVENT_AR24: msk-ca-region.MULE-FLIGHTAWARE-FR24-PRD
      CONNECTOR_CONFIGURATION_FORMAT_FLIGHT_EVENT_AR24_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_FLIGHT_EVENT_AR24: 30

      # fraud screening
      CONNECTOR_CONFIGURATION_TOPIC_FRAUD_SCREENING_DIR: fraud-screening
      CONNECTOR_CONFIGURATION_TOPIC_FRAUD_SCREENING: msk-ca-region.MULE-XPAY-PAYMENTSTATUS-PRD
      CONNECTOR_CONFIGURATION_FORMAT_FRAUD_SCREENING_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_FRAUD_SCREENING: 30

      # tkt
      CONNECTOR_CONFIGURATION_TOPIC_TKT_DIR: tkt
      CONNECTOR_CONFIGURATION_TOPIC_TKT: RAW-TKT-PRODCA1
      CONNECTOR_CONFIGURATION_FORMAT_TKT_CLASS: io.confluent.connect.s3.format.json.JsonFormat
      S3_EVENTS_EXPIRATION_DAYS_TKT: 30

      # XFSU
      CONNECTOR_CONFIGURATION_TOPIC_XFSU_DIR: xfsu
      CONNECTOR_CONFIGURATION_TOPIC_XFSU: msk-ca-region.ECARGO-FSU-JSON-PRD
      CONNECTOR_CONFIGURATION_FORMAT_XFSU_CLASS: io.confluent.connect.s3.format.json.JsonFormat

      CRAWLER_ROLE_NAME: ac-odh-event-streaming-services-msk-connect-res-prodca1-catalog