{"name": "ac-odh-event-streaming-services-msk-connect-tests", "version": "1.0.0", "description": "Unit tests for Air Canada ODH Event Streaming Services MSK Connect infrastructure", "main": "index.js", "scripts": {"test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --coverage --ci --reporters=default --reporters=jest-junit", "lint:yaml": "yamllint stacks/**/*.yml defaults.yml", "lint:cfn": "cfn-lint stacks/**/*.yml", "validate:serverless": "node tests/utils/validateServerless.js", "test:all": "npm run lint:yaml && npm run lint:cfn && npm run test:coverage"}, "keywords": ["aws", "msk", "kafka", "serverless", "infrastructure", "testing"], "author": "Air Canada Digital Team", "license": "UNLICENSED", "devDependencies": {"jest": "^29.7.0", "jest-junit": "^16.0.0", "@types/jest": "^29.5.5", "js-yaml": "^4.1.0", "yaml-lint": "^1.7.0", "cfn-lint": "1.9.7", "serverless": "^3.38.0", "aws-sdk": "^2.1490.0", "lodash": "^4.17.21", "glob": "^10.3.10", "jest-sonar-reporter": "^2.0.0"}, "jest": {"collectCoverage": true, "coveragePathIgnorePatterns": ["/node_modules/", "/test/", "/coverage/"], "moduleDirectories": ["node_modules"], "testResultsProcessor": "jest-sonar-reporter"}, "jestSonar": {"reportPath": "coverage", "reportFile": "reporter.xml", "indent": 4}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}