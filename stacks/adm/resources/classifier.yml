
Type: AWS::Glue::Classifier
DependsOn: GlueDatabaseAdm
Properties:
  GrokClassifier:
    Name: s3_classifier_adm_v3
    Classification: grok
    GrokPattern: <fnCarrier>%{DATA:fnCarrier}</fnCarrier>.*?<fnNumber>%{NUMBER:fnNumber}</fnNumber>%{FNSUFFIX_TAG}.*?</flight>.*?<dayOfOrigin>%{CUSTOM_ISO_DATE:dayOfOrigin}</dayOfOrigin>(?:.*?<localDayOfOrigin>%{CUSTOM_ISO_DATE:localDayOfOrigin}</localDayOfOrigin>)?.*?%{CAROUSEL_TAG}
    CustomPatterns: |
      CUSTOM_ISO_DATE %{YEAR}-%{MONTHNUM2}-%{MONTHDAY}
      CAROUSEL_TAG (?:(?:<carousel>%{DATA:carousel}</carousel>)|(?:<carousel/>))
      FNSUFFIX_TAG (?:.*?<fnSuffix>%{DATA:fnSuffix}</fnSuffix>)?
    

