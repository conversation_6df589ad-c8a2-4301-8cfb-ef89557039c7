Type: AWS::Glue::Crawler
DependsOn: GlueDatabaseAdm
Properties:
  Name: s3_crawler_adm_v3
  Role: arn:aws:iam::${aws:accountId}:role/${self:custom.defaultParams.${self:provider.stage}.CRAWLER_ROLE_NAME}
  DatabaseName: !Ref GlueDatabaseAdm
  Configuration: "{\"Version\":1.0,\"CrawlerOutput\":{\"Partitions\":{\"AddOrUpdateBehavior\":\"InheritFromTable\"},\"Tables\":{\"AddOrUpdateBehavior\":\"MergeNewColumns\"}}}"
  Targets:
    S3Targets:
      - Path: ${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_BUCKET}/${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_TOPIC_ADM_DIR}/${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_TOPIC_ADM}
  Classifiers:
      - !Ref CustomClassifierAdm
  Schedule:
    ScheduleExpression: cron(15 0 ? * 7 *)