Type: AWS::Glue::Crawler
Properties:
  Name: s3_tables_cm_feed_crawler
  Role: arn:aws:iam::${aws:accountId}:role/${self:custom.defaultParams.${self:provider.stage}.CRAWLER_ROLE_NAME}
  DatabaseName: !Ref GlueDatabaseCmFeed
  Configuration: >
    {
      "Version": 1.0,
      "Grouping": {
        "TableGroupingPolicy": "CombineCompatibleSchemas"
      },
      "CrawlerOutput": {
        "Partitions": {
          "AddOrUpdateBehavior": "InheritFromTable"
        },
        "Tables": {
          "AddOrUpdateBehavior": "MergeNewColumns"
        }
      }
    }
  Targets:
    CatalogTargets:
      - DatabaseName: !Ref GlueDatabaseCmFeed
        Tables:
            - s3_cm_feed_table
  SchemaChangePolicy:
    UpdateBehavior: "LOG"
    DeleteBehavior: "LOG"

  Schedule:
    ScheduleExpression:  cron(15 0 ? * 7 *)