Type: AWS::Glue::Table
Properties:
  CatalogId: !Ref AWS::AccountId
  DatabaseName: !Ref GlueDatabaseCmFeed
  TableInput:
    Name: s3_cm_feed_table
    StorageDescriptor:
      Columns:
        - Name: meta
          Type: string
        - Name: previousRecord
          Type: string
        - Name: processedDcsPassenger
          Type: string
        - Name: lastModification
          Type: string
        - Name: events
          Type: string
      Location: s3://${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_BUCKET}/${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_TOPIC_CM_FEED_DIR}/${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_TOPIC_CM_FEED}
      InputFormat: org.apache.hadoop.mapred.TextInputFormat
      OutputFormat: org.apache.hadoop.hive.ql.io.HiveIgnoreKeyTextOutputFormat
      SerdeInfo:
        SerializationLibrary: org.openx.data.jsonserde.JsonSerDe
        Parameters:
          paths: "meta,previousRecord,processedDcsPassenger,lastModification,events"
    PartitionKeys:
      - Name: year
        Type: string
      - Name: month
        Type: string
      - Name: day
        Type: string
      - Name: hour
        Type: string
    TableType: EXTERNAL_TABLE
    Parameters:
      classification: json
      EXTERNAL: "TRUE"