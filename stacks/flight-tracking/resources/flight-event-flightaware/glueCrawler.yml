Type: AWS::Glue::Crawler
DependsOn:
  - glueDatabaseFlightTracker
Properties:
  Name: s3_tables_flight_tracker_crawler
  Role: arn:aws:iam::${aws:accountId}:role/${self:custom.defaultParams.${self:provider.stage}.CRAWLER_ROLE_NAME}
  DatabaseName: !Ref glueDatabaseFlightTracker
  Configuration: "{\"Version\":1.0,\"Grouping\":{\"TableGroupingPolicy\":\"CombineCompatibleSchemas\"}, \"CrawlerOutput\":{\"Partitions\":{\"AddOrUpdateBehavior\":\"InheritFromTable\"},\"Tables\":{\"AddOrUpdateBehavior\":\"MergeNewColumns\"}}}"
  Targets:
    S3Targets:
      - Path: ${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_BUCKET}/${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_TOPIC_FLIGHT_EVENT_FLIGHTAWARE_DIR}/${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_TOPIC_FLIGHT_EVENT_FLIGHTAWARE}
  Schedule:
    ScheduleExpression: cron(15 0 ? * 7 *)
