Type: AWS::KafkaConnect::Connector
DependsOn:
  - logGroupFlightEventFlightAware
Properties:
    Capacity:
      ProvisionedCapacity:
        WorkerCount: ${self:custom.defaultParams.${self:provider.stage}.CAPACITY_PROVISIONED_CAPACITY_WORKER_COUNT}
        McuCount: ${self:custom.defaultParams.${self:provider.stage}.CAPACITY_PROVISIONED_CAPACITY_MCU_COUNT}
    #          AutoScaling:
    #            MaxWorkerCount: ${self:custom.defaultParams.${self:provider.stage}.CAPACITY_AUTO_SCALING_MAX_WORKER_COUNT}
    #            McuCount: ${self:custom.defaultParams.${self:provider.stage}.CAPACITY_AUTO_SCALING_MCU_COUNT}
    #            MinWorkerCount: ${self:custom.defaultParams.${self:provider.stage}.CAPACITY_AUTO_SCALING_MIN_WORDER_COUNT}
    #            ScaleInPolicy:
    #              CpuUtilizationPercentage: ${self:custom.defaultParams.${self:provider.stage}.CAPACITY_AUTO_SCALING_SCALE_IN_POLICY_CPU_UTIL_PERCENTAGE}
    #            ScaleOutPolicy:
    #              CpuUtilizationPercentage: ${self:custom.defaultParams.${self:provider.stage}.CAPACITY_AUTO_SCALING_SCALE_OUT_POLICY_CPU_UTIL_PERCENTAGE}
    ConnectorConfiguration:
      connector.class: io.confluent.connect.s3.S3SinkConnector
      s3.region: ${self:provider.region}
      behavior.on.null.values : ignore
      partition.duration.ms: 1000
      topics.dir: ${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_TOPIC_FLIGHT_EVENT_FLIGHTAWARE_DIR}
      flush.size: ${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_FLUSH_SIZE}
      schema.compatibility: NONE
      topics: ${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_TOPIC_FLIGHT_EVENT_FLIGHTAWARE}
      timezone: UTC
      tasks.max: 4
      rotate.interval.ms: -1
      locale: en_CA
      format.class: ${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_FORMAT_FLIGHT_EVENT_FLIGHTAWARE_CLASS}
      partitioner.class: io.confluent.connect.storage.partitioner.TimeBasedPartitioner
      value.converter: org.apache.kafka.connect.json.JsonConverter
      value.converter.schemas.enable: false
      storage.class: io.confluent.connect.s3.storage.S3Storage
      s3.bucket.name: ${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_BUCKET}
      path.format: "'year'=YYYY/'month'=MM/'day'=dd/'hour'=HH"
      key.converter: org.apache.kafka.connect.storage.StringConverter
    ConnectorName: ${self:service}-fe-fa-to-s3-${self:provider.stage}
    KafkaCluster:
      ApacheKafkaCluster:
        BootstrapServers: ${self:custom.defaultParams.${self:provider.stage}.ORIGIN_BOOTSTRAP_SERVER}
        Vpc:
          SecurityGroups:
            - ${self:custom.defaultParams.${self:provider.stage}.ORIGIN_VPC_SG}
          Subnets:
            - ${self:custom.defaultParams.${self:provider.stage}.ORIGIN_VPC_SUBNET_A}
            - ${self:custom.defaultParams.${self:provider.stage}.ORIGIN_VPC_SUBNET_B}
            - ${self:custom.defaultParams.${self:provider.stage}.ORIGIN_VPC_SUBNET_C}
    KafkaClusterClientAuthentication:
      AuthenticationType: NONE
    KafkaClusterEncryptionInTransit:
      EncryptionType: TLS
    KafkaConnectVersion: "2.7.1"
    LogDelivery:
      WorkerLogDelivery:
        CloudWatchLogs:
          Enabled: true
          LogGroup: !Ref logGroupFlightEventFlightAware
    Plugins:
      -
        CustomPlugin:
          CustomPluginArn: ${self:custom.defaultParams.${self:provider.stage}.CUSTOM_PLUGIN_ARN}
          Revision: ${self:custom.defaultParams.${self:provider.stage}.CUSTOM_PLUGIN_REVISION}
    ServiceExecutionRoleArn: arn:aws:iam::${aws:accountId}:role/ac-odh-event-streaming-services-msk-connect-res-${self:provider.stage}-snk