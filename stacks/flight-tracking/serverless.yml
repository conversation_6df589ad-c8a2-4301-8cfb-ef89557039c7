service: ${file(../../defaults.yml):service}-flight-tracking
variablesResolutionMode: 20210326

provider:
  name: aws
  region: ${opt:region, "ca-central-1"}
  stage: ${opt:stage, "intca1"}
  deploymentBucket:
    name: ${cf:ac-reusable-deployment-bucket-${self:provider.stage}.ACDigitalBucketName}

custom:
  defaults: ${file(../../defaults.yml)}
  defaultParams: ${self:custom.defaults.custom.params}
  tags: ${self:custom.defaults.custom.tags}
  base: ${self:service}-${self:provider.stage}

resources:
  Resources:
    glueDatabaseFlightTracker: ${file(./resources/common/glueDatabase.yml)}

#    Flight Event - Flightaware
    glueCrawlerFlightEventFLightaware: ${file(./resources/flight-event-flightaware/glueCrawler.yml)}
    logGroupFlightEventFlightAware:    ${file(./resources/flight-event-flightaware/logGroup.yml)}
    mskConnectFlightEventFlightAware:  ${file(./resources/flight-event-flightaware/mskConnect.yml)}

#    Weather Event - Flightaware
    glueCrawlerWeatherEventFlightaware: ${file(./resources/weather-event-flightaware/glueCrawler.yml)}
    logGroupWeatherFlightAware: ${file(./resources/weather-event-flightaware/logGroup.yml)}
    mskConnectWeatherFlightAware: ${file(./resources/weather-event-flightaware/mskConnect.yml)}

#    Flight Event - AR24
    glueCrawlerFlightEventAR24: ${file(./resources/flight-event-ar24/glueCrawler.yml)}
    logGroupFlightEventAR24: ${file(./resources/flight-event-ar24/logGroup.yml)}
    mskConnectFlightEventAR24: ${file(./resources/flight-event-ar24/mskConnect.yml)}