Type: AWS::Glue::Table
DeletionPolicy: Delete
DependsOn:
  - GlueDatabaseFraud
  - GlueTableFraud
Properties:
  CatalogId: !Ref AWS::AccountId
  DatabaseName: ${self:custom.DatabaseName}
  TableInput:
    Name: fraudprofile
    TableType: VIRTUAL_VIEW
    Parameters:
      presto_view: "true"
      comment: "Fraud screening view"
    StorageDescriptor:
      Columns:
        - Name: TransactionId
          Type: string
        - Name: BookingRecordLocator
          Type: string
        - Name: BookingCreatedAt
          Type: string  
        - Name: LoyaltyProfileMembershipIdentifier
          Type: string
        - Name: LoyaltyProgramCarrierCode
          Type: string
        - Name: PaymentRecordId
          Type: string
        - Name: OfficeId
          Type: string
        - Name: PointOfSale
          Type: string
        - Name: Channel
          Type: string
        - Name: FormOfPaymentTypeCode
          Type: string
        - Name: PaymentCardNumber
          Type: string
        - Name: PaymentCardTypeCode
          Type: string
        - Name: EmailEndpointAddress
          Type: string
        - Name: EndPointEmails
          Type: string
        - Name: Amount
          Type: string
        - Name: CurrencyCode
          Type: string
        - Name: FraudProfileTransactionId
          Type: string
        - Name: ThreeDomainSecureRequestStatusCode
          Type: string
        - Name: ThreeDomainSecureRequestCreated
          Type: string
        - Name: ThreeDomainSecureRequestId
          Type: string
        - Name: ThreeDomainSecureResponseStatusCode
          Type: string
        - Name: ThreeDomainSecureResponseCreatedAt
          Type: string
        - Name: ThreeDomainSecureResponseId
          Type: string
        - Name: FraudScreeningRequestStatusCode
          Type: string
        - Name: FraudScreeningRequestCreatedAt
          Type: string
        - Name: FraudScreeningRequestId
          Type: string
        - Name: FraudScreeningResponseStatusCode
          Type: string
        - Name: FraudScreeningResponseCreatedAt
          Type: string
        - Name: FraudScreeningResponseId
          Type: string
        - Name: FraudScreeningCaseUpdate
          Type: string
        - Name: FraudScreeningCaseUpdatedAt
          Type: string
        - Name: FraudScreeningUpdateId
          Type: string
        - Name: AuthorizationRequestStatusCode
          Type: string
        - Name: AuthorizationRequestCreatedAt
          Type: string
        - Name: AuthorizationRequestId
          Type: string
        - Name: AuthorizationResponseStatusCode
          Type: string
        - Name: AuthorizationResponseCreatedAt
          Type: string
        - Name: AuthorizationResponseId
          Type: string
        - Name: ApplicationEndpoint
          Type: string
        - Name: ErrorCode
          Type: string
        - Name: ErrorMessage
          Type: string
        - Name: RecordCreatedAt
          Type: string
        - Name: day
          Type: string
        - Name: month
          Type: string
        - Name: year
          Type: string
    ViewOriginalText: !Join
      - ""
      - - "/* Presto View: "
        - !Base64 
          Fn::Sub: |-
            {
              "originalSql": "${self:custom.fraudScreeningSQL}",
              "catalog": "awsdatacatalog",
              "schema": "${self:custom.DatabaseName}",
              "columns": [
                {"name": "TransactionId", "type": "varchar"},
                {"name": "BookingRecordLocator", "type": "varchar"},
                {"name": "BookingCreatedAt", "type": "varchar"},
                {"name": "LoyaltyProfileMembershipIdentifier", "type": "varchar"},
                {"name": "LoyaltyProgramCarrierCode", "type": "varchar"},
                {"name": "PaymentRecordId", "type": "varchar"},
                {"name": "OfficeId", "type": "varchar"},
                {"name": "PointOfSale", "type": "varchar"},
                {"name": "Channel", "type": "varchar"},
                {"name": "FormOfPaymentTypeCode", "type": "varchar"},
                {"name": "PaymentCardNumber", "type": "varchar"},
                {"name": "PaymentCardTypeCode", "type": "varchar"},
                {"name": "EmailEndpointAddress", "type": "varchar"},
                {"name": "EndPointEmails", "type": "varchar"},
                {"name": "Amount", "type": "varchar"},
                {"name": "CurrencyCode", "type": "varchar"},
                {"name": "FraudProfileTransactionId", "type": "varchar"},
                {"name": "ThreeDomainSecureRequestStatusCode", "type": "varchar"},
                {"name": "ThreeDomainSecureRequestCreated", "type": "varchar"},
                {"name": "ThreeDomainSecureRequestId", "type": "varchar"},
                {"name": "ThreeDomainSecureResponseStatusCode", "type": "varchar"},
                {"name": "ThreeDomainSecureResponseCreatedAt", "type": "varchar"},
                {"name": "ThreeDomainSecureResponseId", "type": "varchar"},
                {"name": "FraudScreeningRequestStatusCode", "type": "varchar"},
                {"name": "FraudScreeningRequestCreatedAt", "type": "varchar"},
                {"name": "FraudScreeningRequestId", "type": "varchar"},
                {"name": "FraudScreeningResponseStatusCode", "type": "varchar"},
                {"name": "FraudScreeningResponseCreatedAt", "type": "varchar"},
                {"name": "FraudScreeningResponseId", "type": "varchar"},
                {"name": "FraudScreeningCaseUpdate", "type": "varchar"},
                {"name": "FraudScreeningCaseUpdatedAt", "type": "varchar"},
                {"name": "FraudScreeningUpdateId", "type": "varchar"},
                {"name": "AuthorizationRequestStatusCode", "type": "varchar"},
                {"name": "AuthorizationRequestCreatedAt", "type": "varchar"},
                {"name": "AuthorizationRequestId", "type": "varchar"},
                {"name": "AuthorizationResponseStatusCode", "type": "varchar"},
                {"name": "AuthorizationResponseCreatedAt", "type": "varchar"},
                {"name": "AuthorizationResponseId", "type": "varchar"},
                {"name": "ApplicationEndpoint", "type": "varchar"},
                {"name": "ErrorCode", "type": "varchar"},
                {"name": "ErrorMessage", "type": "varchar"},
                {"name": "RecordCreatedAt", "type": "timestamp(3)"},
                {"name": "day", "type": "varchar"},
                {"name": "month", "type": "varchar"},
                {"name": "year", "type": "varchar"}
              ]
            }
        - " */"
    ViewExpandedText: !Join
      - ""
      - - "/* Presto View: "
        - !Base64 
          Fn::Sub: |-
            {
              "originalSql": "${self:custom.fraudScreeningSQL}",
              "catalog": "awsdatacatalog",
              "schema": "${self:custom.DatabaseName}",
              "columns": [
                {"name": "TransactionId", "type": "varchar"},
                {"name": "BookingRecordLocator", "type": "varchar"},
                {"name": "BookingCreatedAt", "type": "varchar"},
                {"name": "LoyaltyProfileMembershipIdentifier", "type": "varchar"},
                {"name": "LoyaltyProgramCarrierCode", "type": "varchar"},
                {"name": "PaymentRecordId", "type": "varchar"},
                {"name": "OfficeId", "type": "varchar"},
                {"name": "PointOfSale", "type": "varchar"},
                {"name": "Channel", "type": "varchar"},
                {"name": "FormOfPaymentTypeCode", "type": "varchar"},
                {"name": "PaymentCardNumber", "type": "varchar"},
                {"name": "PaymentCardTypeCode", "type": "varchar"},
                {"name": "EmailEndpointAddress", "type": "varchar"},
                {"name": "EndPointEmails", "type": "varchar"},
                {"name": "Amount", "type": "varchar"},
                {"name": "CurrencyCode", "type": "varchar"},
                {"name": "FraudProfileTransactionId", "type": "varchar"},
                {"name": "ThreeDomainSecureRequestStatusCode", "type": "varchar"},
                {"name": "ThreeDomainSecureRequestCreated", "type": "varchar"},
                {"name": "ThreeDomainSecureRequestId", "type": "varchar"},
                {"name": "ThreeDomainSecureResponseStatusCode", "type": "varchar"},
                {"name": "ThreeDomainSecureResponseCreatedAt", "type": "varchar"},
                {"name": "ThreeDomainSecureResponseId", "type": "varchar"},
                {"name": "FraudScreeningRequestStatusCode", "type": "varchar"},
                {"name": "FraudScreeningRequestCreatedAt", "type": "varchar"},
                {"name": "FraudScreeningRequestId", "type": "varchar"},
                {"name": "FraudScreeningResponseStatusCode", "type": "varchar"},
                {"name": "FraudScreeningResponseCreatedAt", "type": "varchar"},
                {"name": "FraudScreeningResponseId", "type": "varchar"},
                {"name": "FraudScreeningCaseUpdate", "type": "varchar"},
                {"name": "FraudScreeningCaseUpdatedAt", "type": "varchar"},
                {"name": "FraudScreeningUpdateId", "type": "varchar"},
                {"name": "AuthorizationRequestStatusCode", "type": "varchar"},
                {"name": "AuthorizationRequestCreatedAt", "type": "varchar"},
                {"name": "AuthorizationRequestId", "type": "varchar"},
                {"name": "AuthorizationResponseStatusCode", "type": "varchar"},
                {"name": "AuthorizationResponseCreatedAt", "type": "varchar"},
                {"name": "AuthorizationResponseId", "type": "varchar"},
                {"name": "ApplicationEndpoint", "type": "varchar"},
                {"name": "ErrorCode", "type": "varchar"},
                {"name": "ErrorMessage", "type": "varchar"},
                {"name": "RecordCreatedAt", "type": "timestamp(3)"},
                {"name": "day", "type": "varchar"},
                {"name": "month", "type": "varchar"},
                {"name": "year", "type": "varchar"}
              ]
            }
        - " */"