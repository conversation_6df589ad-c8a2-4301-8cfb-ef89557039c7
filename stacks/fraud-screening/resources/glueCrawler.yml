Type: AWS::Glue::Crawler
Properties:
  Name: s3_tables_fraud_crawler
  Role: arn:aws:iam::${aws:accountId}:role/${self:custom.defaultParams.${self:provider.stage}.CRAWLER_ROLE_NAME}
  DatabaseName: !Ref GlueDatabaseFraud
  Configuration: |
    {
      "Version": 1.0,
      "CrawlerOutput": {
        "Partitions": {
          "AddOrUpdateBehavior": "InheritFromTable"
        }
      },
      "Grouping": {
        "TableGroupingPolicy": "CombineCompatibleSchemas"
      }
    }
  SchemaChangePolicy:
    UpdateBehavior: UPDATE_IN_DATABASE
    DeleteBehavior: DEPRECATE_IN_DATABASE
  Targets:
    S3Targets:
      - Path: ${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_BUCKET}/${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_TOPIC_FRAUD_SCREENING_DIR}/${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_TOPIC_FRAUD_SCREENING}
  Schedule:
    ScheduleExpression: cron(15 0 * * ? *)
  Tags:
    aws-service: Glue Crawler
    unique-id: ${self:custom.base}-crawler
