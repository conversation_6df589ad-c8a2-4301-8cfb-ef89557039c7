Type: AWS::Glue::Table
Properties:
  CatalogId: !Ref AWS::AccountId
  DatabaseName: !Ref GlueDatabaseFraud
  TableInput:
    Name: ${self:custom.TableName}
    TableType: EXTERNAL_TABLE
    Parameters:
      classification: json
      typeOfData: file
      compressionType: none
    StorageDescriptor:
      Columns:
        - Name: paymentdata
          Type: string # Using string to handle any JSON structure
          Comment: Payment data as JSON string
        - Name: events
          Type: string # Using string to handle any JSON structure
          Comment: Events data as JSON string
        - Name: event
          Type: string # For files that have "Event" instead of "Events"
          Comment: Single event data as JSON string
        - Name: fraudscreening_data
          Type: string # For files that have fraud screening data
          Comment: Fraud screening data as JSON string

      Location: s3://${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_BUCKET}/${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_TOPIC_FRAUD_SCREENING_DIR}/${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_TOPIC_FRAUD_SCREENING}/
      InputFormat: org.apache.hadoop.mapred.TextInputFormat
      OutputFormat: org.apache.hadoop.hive.ql.io.HiveIgnoreKeyTextOutputFormat
      SerdeInfo:
        SerializationLibrary: org.openx.data.jsonserde.JsonSerDe
        Parameters:
          # Handle malformed JSON and case sensitivity
          ignore.malformed.json: "true"
          case.insensitive: "false"
          mapping.paymentdata: PaymentData
          mapping.events: Events
          mapping.event: Event
          mapping.fraudscreening_data: FraudScreening_Data
      StoredAsSubDirectories: false
    PartitionKeys:
      - Name: year
        Type: string
      - Name: month
        Type: string
      - Name: day
        Type: string
