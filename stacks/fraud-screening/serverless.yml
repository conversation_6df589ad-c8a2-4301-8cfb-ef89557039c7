service: ${file(../../defaults.yml):service}-fraud

provider:
  name: aws
  region: ${opt:region, "ca-central-1"}
  stage: ${opt:stage, "intca1"}
  stackTags: ${self:custom.tags}
  deploymentBucket:
    name: ${cf:ac-reusable-deployment-bucket-${self:provider.stage}.ACDigitalBucketName}

custom:
  defaults: ${file(../../defaults.yml)}
  defaultParams: ${self:custom.defaults.custom.params}
  tags: ${self:custom.defaults.custom.tags}
  base: ${self:service}-${self:provider.stage}
  # Table name per environment, its the same name as Kafka topic but all in lower case
  tableName:
    intca1: emh_dev_mule_xpay_paymentstatus_int
    batca1: emh_dev_mule_xpay_paymentstatus_uat
    crtca1: emh_dev_mule_xpay_paymentstatus_uat
    prodca1: msk_ca_region_mule_xpay_paymentstatus_prd
  DatabaseName: ac-odh-event-stream-db-fraud-screening 
  TableName: ${self:custom.tableName.${self:provider.stage}}
  fraudScreeningSQL: >-
    SELECT \"events\".\"ac-transaction-id\" AS TransactionId, 
    paymentdata.bookingreferenceid AS BookingRecordLocator, 
    CASE WHEN paymentdata.bookingdatetime LIKE '%T%' THEN replace(paymentdata.bookingdatetime, 'T', ' ') WHEN length(paymentdata.bookingdatetime) = 14 THEN date_format(parse_datetime(paymentdata.bookingdatetime, 'ddMMyyyyHHmmss'), '%Y-%m-%d %H:%i:%s') WHEN length(split(paymentdata.bookingdatetime, ' ')[1]) = 14 THEN date_format(parse_datetime(split(paymentdata.bookingdatetime, ' ')[1], 'ddMMyyyyHHmmss'), '%Y-%m-%d %H:%i:%s') ELSE paymentdata.bookingdatetime END AS BookingCreatedAt,
    paymentdata.membershipnum AS LoyaltyProfileMembershipIdentifier,
    'AC' AS LoyaltyProgramCarrierCode,
    paymentdata.pri AS PaymentRecordId,
    paymentdata.officeid AS OfficeId,
    paymentdata.pos AS PointOfSale,
    paymentdata.clientapp AS Channel,
    paymentdata.foptype AS FormOfPaymentTypeCode,
    paymentdata.fop AS PaymentCardNumber,
    paymentdata.cardtype AS PaymentCardTypeCode,
    paymentdata.emailid AS EmailEndpointAddress,
    paymentdata.emailid AS EndPointEmails,
    paymentdata.amount AS Amount,
    paymentdata.currency AS CurrencyCode,
    \"events\".\"ac-transaction-id\" AS FraudProfileTransactionId,
    \"events\".\"3DSRequestEvent\" AS ThreeDomainSecureRequestStatusCode,
    date_format(parse_datetime(split(events.\"3DSRequestTime\", ' ')[1], 'ddMMyyyyHHmmss'), '%Y-%m-%d %H:%i:%s') AS ThreeDomainSecureRequestCreated,
    events.\"3DSRequestID\" AS ThreeDomainSecureRequestId,
    events.\"3DSResponseEvent\" AS ThreeDomainSecureResponseStatusCode,
    date_format(parse_datetime(split(events.\"3DSResponseTime\", ' ')[1], 'ddMMyyyyHHmmss'), '%Y-%m-%d %H:%i:%s') AS ThreeDomainSecureResponseCreatedAt,
    CAST(NULL AS VARCHAR) AS ThreeDomainSecureResponseId,
    CAST(NULL AS VARCHAR) AS FraudScreeningRequestStatusCode,
    date_format(parse_datetime(split(events.\"FraudScreeningRequestTime\", ' ')[1], 'ddMMyyyyHHmmss'), '%Y-%m-%d %H:%i:%s') AS FraudScreeningRequestCreatedAt,
    CAST(NULL AS VARCHAR) AS FraudScreeningRequestId,
    \"events\".\"FraudScreeningResponseEvent\" AS FraudScreeningResponseStatusCode,
    date_format(parse_datetime(split(events.\"FraudScreeningResponseTime\", ' ')[1], 'ddMMyyyyHHmmss'), '%Y-%m-%d %H:%i:%s') AS FraudScreeningResponseCreatedAt,
    events.\"FraudScreeningResponseID\" AS FraudScreeningResponseId,
    events.\"FraudScreeningCaseUpdateEvent\" AS FraudScreeningCaseUpdate,
    date_format(parse_datetime(split(events.\"FraudScreeningCaseUpdateTime\", ' ')[1], 'ddMMyyyyHHmmss'), '%Y-%m-%d %H:%i:%s') AS FraudScreeningCaseUpdatedAt,
    events.\"FraudScreeningCaseUpdateID\" AS FraudScreeningUpdateId,
    events.\"AuthorizationRequestEvent\" AS AuthorizationRequestStatusCode,
    date_format(parse_datetime(split(events.\"AuthorizationRequestTime\", ' ')[1], 'ddMMyyyyHHmmss'), '%Y-%m-%d %H:%i:%s') AS AuthorizationRequestCreatedAt,
    CAST(NULL AS VARCHAR) AS AuthorizationRequestId,
    events.\"AuthorizationResponseEvent\" AS AuthorizationResponseStatusCode,
    date_format(parse_datetime(split(events.\"AuthorizationResponseTime\", ' ')[1], 'ddMMyyyyHHmmss'), '%Y-%m-%d %H:%i:%s') AS AuthorizationResponseCreatedAt,
    events.\"AuthorizationResponseID\" AS AuthorizationResponseId,
    events.\"ApplicationEndpoint\" AS ApplicationEndpoint,
    events.\"ErrorCode\" AS ErrorCode,
    events.\"ErrorMessage\" AS ErrorMessage,
    DATE_PARSE(CONCAT(year, '-', LPAD(month, 2, '0'), '-', LPAD(day, 2, '0')), '%Y-%m-%d') AS RecordCreatedAt,
    day, 
    month, 
    year 
    FROM \"${self:custom.DatabaseName}\".\"${self:custom.TableName}\"

resources:
  Resources:
    GlueDatabaseFraud: ${file(./resources/glueDatabase.yml)}
    GlueCrawlerFraud: ${file(./resources/glueCrawler.yml)}
    GlueTableFraud: ${file(./resources/glueTable.yml)}
    FraudScreeningView: ${file(./resources/fraudScreeningView.yml)}

    KafkaMskConnectLog:
      Type: AWS::Logs::LogGroup
      Properties:
        LogGroupName: /aws/vendedlogs/msk-connect-s3/${self:custom.base}
        RetentionInDays: 14
        Tags:
          - Key: Application
            Value: Kafka to S3 Transfer MSK Connector for Fraud Screening
          - Key: aws-service
            Value: CloudWatch Logs
          - Key: unique-id
            Value: ${self:custom.base}-log

    KafkaMskConnect:
      Type: AWS::KafkaConnect::Connector
      Properties: 
        Capacity:
          ProvisionedCapacity:
            WorkerCount: ${self:custom.defaultParams.${self:provider.stage}.CAPACITY_PROVISIONED_CAPACITY_WORKER_COUNT}
            McuCount: ${self:custom.defaultParams.${self:provider.stage}.CAPACITY_PROVISIONED_CAPACITY_MCU_COUNT}
        ConnectorConfiguration: 
          connector.class: io.confluent.connect.s3.S3SinkConnector
          s3.region: ${self:provider.region}
          partition.duration.ms: 1000
          topics.dir: ${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_TOPIC_FRAUD_SCREENING_DIR}
          flush.size: ${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_FLUSH_SIZE}
          schema.compatibility: NONE
          topics: ${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_TOPIC_FRAUD_SCREENING}
          timezone: UTC
          tasks.max: 4
          rotate.interval.ms: -1
          locale: en_CA
          format.class: ${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_FORMAT_FRAUD_SCREENING_CLASS}
          partitioner.class: io.confluent.connect.storage.partitioner.TimeBasedPartitioner
          value.converter: org.apache.kafka.connect.json.JsonConverter
          value.converter.schemas.enable: false
          storage.class: io.confluent.connect.s3.storage.S3Storage
          s3.bucket.name: ${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_BUCKET}
          path.format: "'year'=YYYY/'month'=MM/'day'=dd/"
          key.converter: org.apache.kafka.connect.storage.StringConverter
          auto.register.schemas: "false"
        ConnectorName: ${self:service}-kafka-to-s3-${self:provider.stage}
        KafkaCluster: 
          ApacheKafkaCluster:
            BootstrapServers: ${self:custom.defaultParams.${self:provider.stage}.ORIGIN_BOOTSTRAP_SERVER}
            Vpc: 
              SecurityGroups: 
               - ${self:custom.defaultParams.${self:provider.stage}.ORIGIN_VPC_SG}
              Subnets: 
               - ${self:custom.defaultParams.${self:provider.stage}.ORIGIN_VPC_SUBNET_A}
               - ${self:custom.defaultParams.${self:provider.stage}.ORIGIN_VPC_SUBNET_B}
               - ${self:custom.defaultParams.${self:provider.stage}.ORIGIN_VPC_SUBNET_C}
        KafkaClusterClientAuthentication: 
          AuthenticationType: NONE
        KafkaClusterEncryptionInTransit: 
          EncryptionType: TLS
        KafkaConnectVersion: "2.7.1"
        LogDelivery:
          WorkerLogDelivery:
            CloudWatchLogs:
              Enabled: true
              LogGroup: !Ref KafkaMskConnectLog
        Plugins: 
          - 
            CustomPlugin: 
              CustomPluginArn: ${self:custom.defaultParams.${self:provider.stage}.CUSTOM_PLUGIN_ARN}
              Revision: ${self:custom.defaultParams.${self:provider.stage}.CUSTOM_PLUGIN_REVISION}
        ServiceExecutionRoleArn: arn:aws:iam::${aws:accountId}:role/ac-odh-event-streaming-services-msk-connect-res-${self:provider.stage}-snk
        Tags:
          - Key: Application
            Value: Kafka to S3 Transfer MSK Connector for Fraud Screening
          - Key: aws-service
            Value: Kafka Connect
          - Key: unique-id
            Value: ${self:custom.base}-connector
