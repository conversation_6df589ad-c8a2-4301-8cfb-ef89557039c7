service: ${file(../../defaults.yml):service}-ifly-tierstatus
variablesResolutionMode: 20210326

provider:
  name: aws
  region: ${opt:region, "ca-central-1"}
  stage: ${opt:stage, "intca1"}
  deploymentBucket:
    name: ${cf:ac-reusable-deployment-bucket-${self:provider.stage}.ACDigitalBucketName}

custom:
  defaults: ${file(../../defaults.yml)}
  defaultParams: ${self:custom.defaults.custom.params}
  tags: ${self:custom.defaults.custom.tags}
  base: ${self:service}-${self:provider.stage}

resources:
  Resources:
    GlueDatabaseIflyTierstatus: ${file(./resources/glueDatabase.yml)}
    GlueCrawlerIflyTierstatus: ${file(./resources/glueCrawler.yml)}

    KafkaMskConnectLog:
      Type: AWS::Logs::LogGroup
      Properties:
        LogGroupName: /aws/vendedlogs/msk-connect-s3/${self:custom.base}

    KafkaMskConnect:
      Type: AWS::KafkaConnect::Connector
      Properties: 
        Capacity:
          ProvisionedCapacity:
            WorkerCount: ${self:custom.defaultParams.${self:provider.stage}.CAPACITY_PROVISIONED_CAPACITY_WORKER_COUNT}
            McuCount: ${self:custom.defaultParams.${self:provider.stage}.CAPACITY_PROVISIONED_CAPACITY_MCU_COUNT}
#          AutoScaling:
#            MaxWorkerCount: ${self:custom.defaultParams.${self:provider.stage}.CAPACITY_AUTO_SCALING_MAX_WORKER_COUNT}
#            McuCount: ${self:custom.defaultParams.${self:provider.stage}.CAPACITY_AUTO_SCALING_MCU_COUNT}
#            MinWorkerCount: ${self:custom.defaultParams.${self:provider.stage}.CAPACITY_AUTO_SCALING_MIN_WORDER_COUNT}
#            ScaleInPolicy:
#              CpuUtilizationPercentage: ${self:custom.defaultParams.${self:provider.stage}.CAPACITY_AUTO_SCALING_SCALE_IN_POLICY_CPU_UTIL_PERCENTAGE}
#            ScaleOutPolicy:
#              CpuUtilizationPercentage: ${self:custom.defaultParams.${self:provider.stage}.CAPACITY_AUTO_SCALING_SCALE_OUT_POLICY_CPU_UTIL_PERCENTAGE}
        ConnectorConfiguration: 
          connector.class: io.confluent.connect.s3.S3SinkConnector
          s3.region: ${self:provider.region}
          partition.duration.ms: 1000
          topics.dir: ${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_TOPIC_TIERSTATUS_IFLY_DIR}
          flush.size: ${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_FLUSH_SIZE}
          schema.compatibility: NONE
          topics: ${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_TOPIC_TIERSTATUS_IFLY}
          timezone: UTC
          tasks.max: 4
          rotate.interval.ms: -1
          locale: en_CA
          format.class: ${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_FORMAT_TIERSTATUS_IFLY_CLASS}
          partitioner.class: io.confluent.connect.storage.partitioner.TimeBasedPartitioner
          value.converter: org.apache.kafka.connect.json.JsonConverter
          value.converter.schemas.enable: false
          storage.class: io.confluent.connect.s3.storage.S3Storage
          s3.bucket.name: ${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_BUCKET}
          path.format: "'year'=YYYY/'month'=MM/'day'=dd/'hour'=HH"
          key.converter: org.apache.kafka.connect.storage.StringConverter
          auto.register.schemas: "false"
        ConnectorName: ${self:service}-kafka-to-s3-${self:provider.stage}
        KafkaCluster: 
          ApacheKafkaCluster:
            BootstrapServers: ${self:custom.defaultParams.${self:provider.stage}.ORIGIN_BOOTSTRAP_SERVER}
            Vpc: 
              SecurityGroups: 
               - ${self:custom.defaultParams.${self:provider.stage}.ORIGIN_VPC_SG}
              Subnets: 
               - ${self:custom.defaultParams.${self:provider.stage}.ORIGIN_VPC_SUBNET_A}
               - ${self:custom.defaultParams.${self:provider.stage}.ORIGIN_VPC_SUBNET_B}
               - ${self:custom.defaultParams.${self:provider.stage}.ORIGIN_VPC_SUBNET_C}
        KafkaClusterClientAuthentication: 
          AuthenticationType: NONE
        KafkaClusterEncryptionInTransit: 
          EncryptionType: TLS
        KafkaConnectVersion: "2.7.1"
        LogDelivery:
          WorkerLogDelivery:
            CloudWatchLogs:
              Enabled: true
              LogGroup: !Ref KafkaMskConnectLog
        Plugins: 
          - 
            CustomPlugin: 
              CustomPluginArn: ${self:custom.defaultParams.${self:provider.stage}.CUSTOM_PLUGIN_ARN}
              Revision: ${self:custom.defaultParams.${self:provider.stage}.CUSTOM_PLUGIN_REVISION}
        ServiceExecutionRoleArn: arn:aws:iam::${aws:accountId}:role/ac-odh-event-streaming-services-msk-connect-res-${self:provider.stage}-snk