service: ${file(../../defaults.yml):service}-res
variablesResolutionMode: 20210326

provider:
  name: aws
  region: ${opt:region, "ca-central-1"}
  stage: ${opt:stage, "intca1"}
  deploymentBucket:
    name: ${cf:ac-reusable-deployment-bucket-${self:provider.stage}.ACDigitalBucketName}

custom:
  defaults: ${file(../../defaults.yml)}
  defaultParams: ${self:custom.defaults.custom.params}
  tags: ${self:custom.defaults.custom.tags}
  base: ${self:service}-${self:provider.stage}

resources:
  Resources:
    storeBucket:
      Type: 'AWS::S3::Bucket'
      Properties:
        BucketName: ${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_BUCKET}
        LifecycleConfiguration:
          Rules:
            - Id: FT-FE-AR24
              Status: Enabled
              ExpirationInDays: ${self:custom.defaultParams.${self:provider.stage}.S3_EVENTS_EXPIRATION_DAYS_FLIGHT_EVENT_AR24}
              Prefix: /${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_TOPIC_FLIGHT_EVENT_AR24_DIR}
            - Id: FT-WE-FA
              Status: Enabled
              ExpirationInDays: ${self:custom.defaultParams.${self:provider.stage}.S3_EVENTS_EXPIRATION_DAYS_WEATHER_EVENT_FLIGHTAWARE}
              Prefix: /${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_TOPIC_WEATHER_EVENT_FLIGHTAWARE_DIR}
            - Id: FT-FE-FA
              Status: Enabled
              ExpirationInDays: ${self:custom.defaultParams.${self:provider.stage}.S3_EVENTS_EXPIRATION_DAYS_FLIGHT_EVENT_FLIGHTAWARE}
              Prefix: /${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_TOPIC_FLIGHT_EVENT_FLIGHTAWARE_DIR}
            - Id: FSUM
              Status: Enabled
              ExpirationInDays: ${self:custom.defaultParams.${self:provider.stage}.S3_EVENTS_EXPIRATION_DAYS_FSUM}
              Prefix: /${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_TOPIC_FSUM_DIR}
            - Id: ALG
              Status: Enabled
              ExpirationInDays: ${self:custom.defaultParams.${self:provider.stage}.S3_EVENTS_EXPIRATION_DAYS_ALG}
              Prefix: /${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_TOPIC_ALG_DIR}
            - Id: AFLVFL
              Status: Enabled
              ExpirationInDays: ${self:custom.defaultParams.${self:provider.stage}.S3_EVENTS_EXPIRATION_DAYS_AFLVFL}
              Prefix: /${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_TOPIC_AFLVFL_DIR}
            - Id: PNR
              Status: Enabled
              ExpirationInDays: ${self:custom.defaultParams.${self:provider.stage}.S3_EVENTS_EXPIRATION_DAYS_PNR}
              Prefix: /${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_TOPIC_PNR_DIR}
            - Id: CSG
              Status: Enabled
              ExpirationInDays: ${self:custom.defaultParams.${self:provider.stage}.S3_EVENTS_EXPIRATION_DAYS_CSG}
              Prefix: /${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_TOPIC_CSG_DIR}
            - Id: FDM
              Status: Enabled
              ExpirationInDays: ${self:custom.defaultParams.${self:provider.stage}.S3_EVENTS_EXPIRATION_DAYS_FDM}
              Prefix: /${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_TOPIC_FDM_DIR}
            - Id: SSM
              Status: Enabled
              ExpirationInDays: ${self:custom.defaultParams.${self:provider.stage}.S3_EVENTS_EXPIRATION_DAYS_SSM}
              Prefix: /${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_TOPIC_SSM_DIR}
            - Id: HA
              Status: Enabled
              ExpirationInDays: ${self:custom.defaultParams.${self:provider.stage}.S3_EVENTS_EXPIRATION_DAYS_HA}
              Prefix: /${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_TOPIC_HA_DIR}
            - Id: MV
              Status: Enabled
              ExpirationInDays: ${self:custom.defaultParams.${self:provider.stage}.S3_EVENTS_EXPIRATION_DAYS_MV}
              Prefix: /${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_TOPIC_MV_DIR}
            - Id: AM
              Status: Enabled
              ExpirationInDays: ${self:custom.defaultParams.${self:provider.stage}.S3_EVENTS_EXPIRATION_DAYS_AM}
              Prefix: /${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_TOPIC_AM_DIR}
            - Id: AS
              Status: Enabled
              ExpirationInDays: ${self:custom.defaultParams.${self:provider.stage}.S3_EVENTS_EXPIRATION_DAYS_AS}
              Prefix: /${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_TOPIC_AS_DIR}
            - Id: CF_STATUS
              Status: Enabled
              ExpirationInDays: ${self:custom.defaultParams.${self:provider.stage}.S3_EVENTS_EXPIRATION_DAYS_CF_STATUS}
              Prefix: /${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_TOPIC_CF_STATUS_DIR}
            - Id: CM_FEED
              Status: Enabled
              ExpirationInDays: ${self:custom.defaultParams.${self:provider.stage}.S3_EVENTS_EXPIRATION_DAYS_CM_FEED}
              Prefix: /${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_TOPIC_CM_FEED_DIR}
            - Id: CC
              Status: Enabled
              ExpirationInDays: ${self:custom.defaultParams.${self:provider.stage}.S3_EVENTS_EXPIRATION_DAYS_CC}
              Prefix: /${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_TOPIC_CC_DIR}
            - Id: CP_CL1A
              Status: Enabled
              ExpirationInDays: ${self:custom.defaultParams.${self:provider.stage}.S3_EVENTS_EXPIRATION_DAYS_CP_CL1A}
              Prefix: /${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_TOPIC_CP_CL1A_DIR}
            - Id: CP_CL1B
              Status: Enabled
              ExpirationInDays: ${self:custom.defaultParams.${self:provider.stage}.S3_EVENTS_EXPIRATION_DAYS_CP_CL1B}
              Prefix: /${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_TOPIC_CP_CL1B_DIR}
            - Id: FROZEN_STAGE
              Status: Enabled
              ExpirationInDays: ${self:custom.defaultParams.${self:provider.stage}.S3_EVENTS_EXPIRATION_DAYS_FROZEN_STAGE}
              Prefix: /${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_TOPIC_FROZEN_STAGE_DIR}
            - Id: TIERSTATUS_IFLY
              Status: Enabled
              ExpirationInDays: ${self:custom.defaultParams.${self:provider.stage}.S3_EVENTS_EXPIRATION_DAYS_TIERSTATUS_IFLY}
              Prefix: /${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_TOPIC_TIERSTATUS_IFLY_DIR}
            - Id: TOTAL_BAL
              Status: Enabled
              ExpirationInDays: ${self:custom.defaultParams.${self:provider.stage}.S3_EVENTS_EXPIRATION_DAYS_TOTAL_BAL}
              Prefix: /${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_TOPIC_TOTAL_BAL_DIR}
            - Id: MEMBERSHIP_STATUS
              Status: Enabled
              ExpirationInDays: ${self:custom.defaultParams.${self:provider.stage}.S3_EVENTS_EXPIRATION_DAYS_MEMBERSHIP_STATUS}
              Prefix: /${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_TOPIC_MEMBERSHIP_STATUS_DIR}
            - Id: POOL
              Status: Enabled
              ExpirationInDays: ${self:custom.defaultParams.${self:provider.stage}.S3_EVENTS_EXPIRATION_DAYS_POOL}
              Prefix: /${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_TOPIC_POOL_DIR}
            - Id: POOL_BALANCE
              Status: Enabled
              ExpirationInDays: ${self:custom.defaultParams.${self:provider.stage}.S3_EVENTS_EXPIRATION_DAYS_POOL_BALANCE}
              Prefix: /${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_TOPIC_POOL_BALANCE_DIR}
            - Id: POOL_PERMISSION
              Status: Enabled
              ExpirationInDays: ${self:custom.defaultParams.${self:provider.stage}.S3_EVENTS_EXPIRATION_DAYS_POOL_PERMISSION}
              Prefix: /${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_TOPIC_POOL_PERMISSION_DIR}
            - Id: POOL_REDEMPTION
              Status: Enabled
              ExpirationInDays: ${self:custom.defaultParams.${self:provider.stage}.S3_EVENTS_EXPIRATION_DAYS_POOL_REDEMPTION}
              Prefix: /${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_TOPIC_POOL_REDEMPTION_DIR}
            - Id: PROFILE
              Status: Enabled
              ExpirationInDays: ${self:custom.defaultParams.${self:provider.stage}.S3_EVENTS_EXPIRATION_DAYS_PROFILE}
              Prefix: /${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_TOPIC_PROFILE_DIR}
            - Id: RETAIL_PARTNERSHIP
              Status: Enabled
              ExpirationInDays: ${self:custom.defaultParams.${self:provider.stage}.S3_EVENTS_EXPIRATION_DAYS_RETAIL_PARTNERSHIP}
              Prefix: /${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_TOPIC_RETAIL_PARTNERSHIP_DIR}
            - Id: RETROCLAIM
              Status: Enabled
              ExpirationInDays: ${self:custom.defaultParams.${self:provider.stage}.S3_EVENTS_EXPIRATION_DAYS_RETROCLAIM}
              Prefix: /${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_TOPIC_RETROCLAIM_DIR}
            - Id: SMARTLOAD
              Status: Enabled
              ExpirationInDays: ${self:custom.defaultParams.${self:provider.stage}.S3_EVENTS_EXPIRATION_DAYS_SMARTLOAD}
              Prefix: /${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_TOPIC_SMARTLOAD_DIR}
            - Id: CCM_AES
              Status: Enabled
              ExpirationInDays: ${self:custom.defaultParams.${self:provider.stage}.S3_EVENTS_EXPIRATION_DAYS_CCM_AES}
              Prefix: /${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_TOPIC_CCM_AES_DIR}
            - Id: CCM_CTR
              Status: Enabled
              ExpirationInDays: ${self:custom.defaultParams.${self:provider.stage}.S3_EVENTS_EXPIRATION_DAYS_CCM_CTR}
              Prefix: /${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_TOPIC_CCM_CTR_DIR}
            - Id: HC
              Status: Enabled
              ExpirationInDays: ${self:custom.defaultParams.${self:provider.stage}.S3_EVENTS_EXPIRATION_DAYS_HC}
              Prefix: /${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_TOPIC_HC_DIR}
            - Id: FRAUD_SCREENING
              Status: Enabled
              ExpirationInDays: ${self:custom.defaultParams.${self:provider.stage}.S3_EVENTS_EXPIRATION_DAYS_FRAUD_SCREENING}
              Prefix: /${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_TOPIC_FRAUD_SCREENING_DIR} 
            - Id: TKT
              Status: Enabled
              ExpirationInDays: ${self:custom.defaultParams.${self:provider.stage}.S3_EVENTS_EXPIRATION_DAYS_TKT}
              Prefix: /${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_TOPIC_TKT_DIR}
            - Id: JCT-ROSTER-OUTPUT
              Status: Enabled
              ExpirationInDays: ${self:custom.defaultParams.${self:provider.stage}.S3_EVENTS_EXPIRATION_DAYS_JCT_ROSTER_OUTPUT}
              Prefix: /${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_TOPIC_JCT_ROSTER_OUTPUT_DIR}
            - Id: HOTEL_CATALOG_DATA
              Status: Enabled
              ExpirationInDays: ${self:custom.defaultParams.${self:provider.stage}.S3_EVENTS_EXPIRATION_DAYS_HOTEL_CATALOG_DATA}
              Prefix: /${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_TOPIC_HOTEL_CATALOG_DATA_DIR}
            - Id: MONTHLY_BLOCK_LIMIT
              Status: Enabled
              ExpirationInDays: ${self:custom.defaultParams.${self:provider.stage}.S3_EVENTS_EXPIRATION_DAYS_MONTHLY_BLOCK_LIMIT}
              Prefix: /${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_TOPIC_MONTHLY_BLOCK_LIMIT_DIR}
    
    StoreBucketPolicy:
      Type: AWS::S3::BucketPolicy
      Properties:
        Bucket:
          Ref: storeBucket
        PolicyDocument:
          Version: "2012-10-17"
          Statement:
            - Effect: Allow
              Principal:
                AWS:
                  - arn:aws:iam::${self:custom.defaultParams.${self:provider.stage}.POWER_BI_AWS_ACCOUNT}:root
              Action:
                - "s3:GetBucketLocation"
                - "s3:GetObject"
                - "s3:ListBucket"
                - "s3:ListBucketMultipartUploads"
                - "s3:ListMultipartUploadParts"
                - "s3:AbortMultipartUpload"
                - "s3:PutObject"
                - "s3:PutBucketPublicAccessBlock"
              Resource:
                - Fn::Join:
                    - ""
                    - - "arn:aws:s3:::"
                      - Ref: storeBucket
                - Fn::Join:
                    - ""
                    - - "arn:aws:s3:::"
                      - Ref: storeBucket
                      - "/*"

    MskConnectExecutionRole:
      Type: AWS::IAM::Role
      Properties:
        RoleName: ${self:custom.base}-snk
        AssumeRolePolicyDocument:
          Version: '2012-10-17'
          Statement:
            - Effect: Allow
              Principal:
                Service:
                  - kafkaconnect.amazonaws.com
              Action:
                - sts:AssumeRole

        Policies:
          - 
            PolicyName: GetBucketPolicy
            PolicyDocument: 
              Version: "2012-10-17"
              Statement:
              -
                Effect: "Allow"
                Action:
                  - s3:ListBucket
                  - s3:GetBucketLocation
                Resource: !Sub "arn:aws:s3:::${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_BUCKET}"
          - 
            PolicyName: ObjectBucketPolicy
            PolicyDocument: 
              Version: "2012-10-17"
              Statement:
              -
                Effect: "Allow"
                Action:
                  - "s3:PutObject"
                  - "s3:GetObject"
                  - "s3:AbortMultipartUpload"
                  - "s3:ListMultipartUploadParts"
                  - "s3:ListBucketMultipartUploads"
                Resource: !Sub "arn:aws:s3:::${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_BUCKET}/*"

          - 
            PolicyName: KafkaPolicy
            PolicyDocument: 
              Version: "2012-10-17"
              Statement:
              -
                Effect: "Allow"
                Action:
                  - "kafka:DescribeCluster"
                  - "kafka:GetBootstrapBrokers"
                Resource: !Sub ${self:custom.defaultParams.${self:provider.stage}.SERVICE_EXECUTION_ROLE_ARN}

    GlueCrawlerRole:
      Type: "AWS::IAM::Role"
      Properties:
        RoleName: ${self:custom.defaultParams.${self:provider.stage}.CRAWLER_ROLE_NAME}
        AssumeRolePolicyDocument:
          Version: "2012-10-17"
          Statement:
            - Effect: "Allow"
              Principal:
                Service:
                  - "glue.amazonaws.com"
              Action:
                - "sts:AssumeRole"
        Path: "/"
        ManagedPolicyArns:
          [ 'arn:aws:iam::aws:policy/service-role/AWSGlueServiceRole' ]
        Policies:
          - PolicyName: "GlueAccessPolicyUpdate"
            PolicyDocument:
              Version: "2012-10-17"
              Statement:
                - Effect: "Allow"
                  Action:
                    - "glue:GetConnections"
                    - "glue:GetTables"
                    - "glue:GetDatabases"
                  Resource:
                    - !Sub arn:aws:glue:${self:provider.region}:${AWS::AccountId}:catalog
                    - !Sub arn:aws:glue:${self:provider.region}:${AWS::AccountId}:database/s3_ca_datastore
                    - !Sub arn:aws:glue:${self:provider.region}:${AWS::AccountId}:table/s3_ca_datastore/*

          - PolicyName: "GlueAccessPolicyS3"
            PolicyDocument:
              Version: "2012-10-17"
              Statement:
                - Effect: "Allow"
                  Action:
                    - "s3:GetObject"
                    - "s3:PutObject"
                  Resource:
                    - !Sub arn:aws:s3:::${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_BUCKET}/*

          - PolicyName: "WriteLogPolicy"
            PolicyDocument:
              Version: "2012-10-17"
              Statement:
                - Effect: "Allow"
                  Action:
                    - "logs:CreateLogGroup"
                    - "logs:CreateLogStream"
                    - "logs:PutLogEvents"
                  Resource:
                    - "arn:aws:logs:*:*:/aws-glue/*"
          - PolicyName: "CloudwatchAccess"
            PolicyDocument:
              Version: "2012-10-17"
              Statement:
                - Effect: "Allow"
                  Action:
                    - "cloudwatch:PutMetricData"
                  Resource:
                    - "*"
#  Outputs:
#    MskConnectExecutionRoleRef:
#      Value:
#        Ref: MskConnectExecutionRole
#      Export:
#        Name: ${self:provider.stage}-mskConnectExecutionRole-ref