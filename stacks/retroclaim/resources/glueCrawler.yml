Type: AWS::Glue::Crawler
Properties:
  Name: s3_tables_retroclaim_crawler
  Role: arn:aws:iam::${aws:accountId}:role/${self:custom.defaultParams.${self:provider.stage}.CRAWLER_ROLE_NAME}
  DatabaseName: !Ref GlueDatabaseRetroclaim
  Configuration: "{\"Version\":1.0,\"CrawlerOutput\":{\"Partitions\":{\"AddOrUpdateBehavior\":\"InheritFromTable\"},\"Tables\":{\"AddOrUpdateBehavior\":\"MergeNewColumns\"}}}"
  Targets:
    S3Targets:
      - Path: ${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_BUCKET}/${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_TOPIC_RETROCLAIM_DIR}/${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_TOPIC_RETROCLAIM}
  Schedule:
    ScheduleExpression: cron(15 0 ? * 7 *)