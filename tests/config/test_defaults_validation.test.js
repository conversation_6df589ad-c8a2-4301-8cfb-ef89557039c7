/**
 * Defaults Configuration Validation Tests
 * Tests to validate the structure and consistency of defaults.yml
 */

const yamlLoader = require('../utils/yamlLoader');

describe('Defaults Configuration Validation', () => {
  let defaults;
  const expectedEnvironments = ['intca1', 'batca1', 'crtca1', 'preprodca1', 'prodca1'];

  beforeAll(() => {
    defaults = yamlLoader.loadDefaults();
  });

  afterAll(() => {
    yamlLoader.clearCache();
  });

  describe('Basic Structure', () => {
    test('should have required top-level properties', () => {
      expect(defaults).toHaveProperty('service');
      expect(defaults).toHaveProperty('custom');
      expect(defaults.custom).toHaveProperty('tags');
      expect(defaults.custom).toHaveProperty('params');
    });

    test('should have valid service name', () => {
      expect(defaults.service).toBe('ac-odh-event-streaming-services-msk-connect');
    });

    test('should have all required tags', () => {
      const requiredTags = [
        'project', 'application', 'repository', 'environment',
        'tower', 'department-id', 'department-name', 'service',
        'CostCode', 'ProjectName', 'TechOwner', 'BusinessOwner'
      ];

      requiredTags.forEach(tag => {
        expect(defaults.custom.tags).toHaveProperty(tag);
      });
    });
  });

  describe('Environment Configuration', () => {
    test('should have all expected environments', () => {
      expectedEnvironments.forEach(env => {
        expect(defaults.custom.params).toHaveProperty(env);
      });
    });

    test('each environment should have required common parameters', () => {
      const requiredParams = [
        'DEBUG_MODE',
        'POWER_BI_AWS_ACCOUNT',
        'CAPACITY_PROVISIONED_CAPACITY_WORKER_COUNT',
        'CAPACITY_PROVISIONED_CAPACITY_MCU_COUNT',
        'CONNECTOR_CONFIGURATION_FLUSH_SIZE',
        'CONNECTOR_CONFIGURATION_BUCKET',
        'SERVICE_EXECUTION_ROLE_ARN',
        'ORIGIN_BOOTSTRAP_SERVER',
        'ORIGIN_VPC_SG',
        'ORIGIN_VPC_SUBNET_A',
        'ORIGIN_VPC_SUBNET_B',
        'ORIGIN_VPC_SUBNET_C',
        'CUSTOM_PLUGIN_ARN',
        'CUSTOM_PLUGIN_REVISION'
      ];

      expectedEnvironments.forEach(env => {
        const envParams = defaults.custom.params[env];
        
        requiredParams.forEach(param => {
          expect(envParams).toHaveProperty(param);
          expect(envParams[param]).toBeDefined();
        });
      });
    });

    test('AWS account IDs should be valid', () => {
      const awsAccountRegex = /^\d{12}$/;
      
      expectedEnvironments.forEach(env => {
        const accountId = defaults.custom.params[env].POWER_BI_AWS_ACCOUNT;
        expect(accountId.toString()).toMatch(awsAccountRegex);
      });
    });

    test('S3 bucket names should follow naming convention', () => {
      const bucketNameRegex = /^ac-odh-stream-event-store-[a-z0-9]+$/;
      
      expectedEnvironments.forEach(env => {
        const bucketName = defaults.custom.params[env].CONNECTOR_CONFIGURATION_BUCKET;
        expect(bucketName).toMatch(bucketNameRegex);
      });
    });

    test('VPC security groups should be valid format', () => {
      const sgRegex = /^sg-[a-f0-9]{8,17}$/;
      
      expectedEnvironments.forEach(env => {
        const sg = defaults.custom.params[env].ORIGIN_VPC_SG;
        expect(sg).toMatch(sgRegex);
      });
    });

    test('VPC subnets should be valid format', () => {
      const subnetRegex = /^subnet-[a-f0-9]{8,17}$/;
      
      expectedEnvironments.forEach(env => {
        const envParams = defaults.custom.params[env];
        
        expect(envParams.ORIGIN_VPC_SUBNET_A).toMatch(subnetRegex);
        expect(envParams.ORIGIN_VPC_SUBNET_B).toMatch(subnetRegex);
        expect(envParams.ORIGIN_VPC_SUBNET_C).toMatch(subnetRegex);
      });
    });
  });
});
