/**
 * AWS Glue Resources Infrastructure Tests
 * Tests to validate Glue databases, crawlers, and tables
 */

const yamlLoader = require('../utils/yamlLoader');

describe('AWS Glue Resources Infrastructure', () => {
  let stacks;

  beforeAll(() => {
    stacks = yamlLoader.getAllStacks();
  });

  afterAll(() => {
    yamlLoader.clearCache();
  });

  describe('Glue Classifier Resources', () => {
    test('adm stack should have valid Grok classifier', () => {
      const stackResources = yamlLoader.getStackResources('adm');
      
      if (stackResources.includes('classifier')) {
        const classifier = yamlLoader.loadResourceConfig('adm', 'classifier');
        
        expect(classifier).toHaveProperty('Type', 'AWS::Glue::Classifier');
        expect(classifier).toHaveProperty('Properties');
        
        const props = classifier.Properties;
        expect(props).toHaveProperty('GrokClassifier');
        
        const grokClassifier = props.GrokClassifier;
        expect(grokClassifier).toHaveProperty('Name');
        expect(grokClassifier).toHaveProperty('Classification', 'grok');
        expect(grokClassifier).toHaveProperty('GrokPattern');
        expect(grokClassifier).toHaveProperty('CustomPatterns');
        
        // Grok pattern should be non-empty
        expect(grokClassifier.GrokPattern.length).toBeGreaterThan(0);
        
        // Custom patterns should define useful patterns
        expect(grokClassifier.CustomPatterns).toContain('CUSTOM_ISO_DATE');
        expect(grokClassifier.CustomPatterns).toContain('CAROUSEL_TAG');
        expect(grokClassifier.CustomPatterns).toContain('FNSUFFIX_TAG');
      }
    });
  });
});
