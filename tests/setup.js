/**
 * Jest setup file for MSK Connect infrastructure tests
 * This file runs before each test suite
 */

// Set test timeout for longer running tests
jest.setTimeout(30000);

// Mock AWS SDK to prevent actual AWS calls during testing
jest.mock('aws-sdk', () => ({
  CloudFormation: jest.fn(() => ({
    validateTemplate: jest.fn().mockReturnValue({
      promise: jest.fn().mockResolvedValue({
        Parameters: [],
        Capabilities: []
      })
    })
  })),
  S3: jest.fn(() => ({
    headBucket: jest.fn().mockReturnValue({
      promise: jest.fn().mockResolvedValue({})
    })
  })),
  IAM: jest.fn(() => ({
    getRole: jest.fn().mockReturnValue({
      promise: jest.fn().mockResolvedValue({
        Role: { Arn: 'arn:aws:iam::123456789012:role/test-role' }
      })
    })
  })),
  Glue: jest.fn(() => ({
    getDatabase: jest.fn().mockReturnValue({
      promise: jest.fn().mockResolvedValue({
        Database: { Name: 'test-database' }
      })
    })
  })),
  KafkaConnect: jest.fn(() => ({
    describeConnector: jest.fn().mockReturnValue({
      promise: jest.fn().mockResolvedValue({
        ConnectorArn: 'arn:aws:kafkaconnect:ca-central-1:123456789012:connector/test-connector'
      })
    })
  }))
}));

// Global test utilities
global.testUtils = {
  // Common test data
  environments: ['intca1', 'batca1', 'crtca1', 'preprodca1', 'prodca1'],
  
  // Common AWS resource patterns
  arnPatterns: {
    s3Bucket: /^[a-z0-9][a-z0-9-]*[a-z0-9]$/,
    iamRole: /^arn:aws:iam::\d{12}:role\/[a-zA-Z0-9+=,.@_-]+$/,
    kafkaCluster: /^arn:aws:kafka:[a-z0-9-]+:\d{12}:cluster\/[a-zA-Z0-9-]+\/[a-f0-9-]+$/,
    customPlugin: /^arn:aws:kafkaconnect:[a-z0-9-]+:\d{12}:custom-plugin\/[a-zA-Z0-9-]+\/[a-f0-9-]+$/
  },
  
  // Common validation functions
  isValidCronExpression: (expression) => {
    const cronRegex = /^cron\(\s*(\*|[0-5]?\d)\s+(\*|[01]?\d|2[0-3])\s+(\*|\?|[01]?\d|[12]\d|3[01])\s+(\*|[01]?\d|1[0-2])\s+(\*|\?|[0-6])\s+(\*|[12]\d{3})\s*\)$/;
    return cronRegex.test(expression);
  },
  
  isValidLogGroupName: (name) => {
    const logGroupRegex = /^\/aws\/vendedlogs\/msk-connect-s3\/[a-zA-Z0-9-_]+$/;
    return logGroupRegex.test(name);
  }
};

// Console override for cleaner test output
const originalConsoleError = console.error;
console.error = (...args) => {
  // Suppress specific AWS SDK warnings during tests
  if (args[0] && typeof args[0] === 'string' && args[0].includes('AWS SDK')) {
    return;
  }
  originalConsoleError.apply(console, args);
};
