/**
 * YAML Loader utility for loading and parsing Serverless and CloudFormation templates
 */

const fs = require('fs');
const path = require('path');
const yaml = require('js-yaml');
const glob = require('glob');

class YamlLoader {
  constructor() {
    this.cache = new Map();
  }

  loadYaml(filePath) {
    if (this.cache.has(filePath)) {
      return this.cache.get(filePath);
    }

    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const parsed = yaml.load(content);
      this.cache.set(filePath, parsed);
      return parsed;
    } catch (error) {
      throw new Error(`Failed to load YAML file ${filePath}: ${error.message}`);
    }
  }


  loadDefaults() {
    const defaultsPath = path.join(process.cwd(), 'defaults.yml');
    return this.loadYaml(defaultsPath);
  }

  loadResourceConfig(stackName, resourceName) {
    const resourcePath = path.join(process.cwd(), 'stacks', stackName, 'resources', `${resourceName}.yml`);
    return this.loadYaml(resourcePath);
  }

  getAllStacks() {
    const stacksDir = path.join(process.cwd(), 'stacks');
    return fs.readdirSync(stacksDir)
      .filter(item => {
        const itemPath = path.join(stacksDir, item);
        return fs.statSync(itemPath).isDirectory() && 
               fs.existsSync(path.join(itemPath, 'serverless.yml'));
      });
  }

  getStackResources(stackName) {
    const resourcesDir = path.join(process.cwd(), 'stacks', stackName, 'resources');
    if (!fs.existsSync(resourcesDir)) {
      return [];
    }
    
    return fs.readdirSync(resourcesDir)
      .filter(file => file.endsWith('.yml'))
      .map(file => file.replace('.yml', ''));
  }

  clearCache() {
    this.cache.clear();
  }
}

module.exports = new YamlLoader();
